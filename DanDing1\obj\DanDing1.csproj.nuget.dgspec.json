{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\DanDing1.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj", "projectName": "DamoControlKit", "projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"YoloSharp": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\DanDing1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\DanDing1.csproj", "projectName": "DanDing1", "projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\DanDing1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows10.0.17763.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.17763": {"targetAlias": "net8.0-windows10.0.17763.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj"}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\ScriptEngine.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\ScriptEngine.csproj"}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows10.0.17763": {"targetAlias": "net8.0-windows10.0.17763.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Cronos": {"target": "Package", "version": "[0.11.0, )"}, "Hardcodet.NotifyIcon.Wpf": {"target": "Package", "version": "[1.1.0, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Toolkit.Uwp.Notifications": {"target": "Package", "version": "[7.1.3, )"}, "Minio": {"target": "Package", "version": "[6.0.4, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[9.0.5, )"}, "WPF-UI": {"target": "Package", "version": "[4.0.2, )"}, "YoloSharp": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.17763.57, 10.0.17763.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\ScriptEngine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\ScriptEngine.csproj", "projectName": "ScriptEngine", "projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\ScriptEngine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\ScriptEngine\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DamoControlKit\\DamoControlKit.csproj"}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Minio": {"target": "Package", "version": "[6.0.4, )"}, "YoloSharp": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj", "projectName": "XHelper", "projectPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\XHelper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\XHelper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Emgu.CV.Bitmap": {"target": "Package", "version": "[4.10.0.5680, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.21.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.16, )", "autoReferenced": true}, "NLog": {"target": "Package", "version": "[5.4.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.10.0.20241108, )"}, "System.Management": {"target": "Package", "version": "[9.0.2, )"}, "System.Net.WebSockets.Client": {"target": "Package", "version": "[4.3.2, )"}, "System.Private.Uri": {"target": "Package", "version": "[4.3.2, )"}, "Tesseract": {"target": "Package", "version": "[5.2.0, )"}, "YoloSharp": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}