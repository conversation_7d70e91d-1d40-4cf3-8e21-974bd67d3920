<Window
        x:Class="DanDing1.Views.Windows.TaskHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:viewModels="clr-namespace:DanDing1.ViewModels.Windows"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        mc:Ignorable="d"
        Title="定时任务历史记录"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterScreen">

        <Window.DataContext>
                <viewModels:TaskHistoryViewModel/>
        </Window.DataContext>

        <Window.Resources>
                <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

                <!-- 添加GreaterThanOneConverter -->
                <local:GreaterThanOneConverter x:Key="GreaterThanOneConverter"/>

                <!-- 统计卡片样式 - 支持主题 -->
                <Style x:Key="StatCardStyle"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
                        <Setter Property="BorderBrush"
                                Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="CornerRadius"
                                Value="5"/>
                        <Setter Property="Margin"
                                Value="5"/>
                        <Setter Property="Padding"
                                Value="10"/>
                </Style>

                <!-- 统计标题样式 - 支持主题 -->
                <Style x:Key="StatTitleStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource TextFillColorSecondaryBrush}"/>
                        <Setter Property="FontWeight"
                                Value="Medium"/>
                        <Setter Property="Margin"
                                Value="0,0,0,5"/>
                </Style>

                <!-- 统计数值样式 - 支持主题 -->
                <Style x:Key="StatValueStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="20"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Right"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                </Style>

                <!-- 自定义DataGrid样式 - 优化选中效果和主题支持 -->
                <Style x:Key="CustomDataGridStyle"
                       TargetType="DataGrid">
                        <Setter Property="Background"
                                Value="{DynamicResource ApplicationBackgroundBrush}"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                        <Setter Property="BorderBrush"
                                Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="RowBackground"
                                Value="{DynamicResource SubtleFillColorTransparentBrush}"/>
                        <Setter Property="AlternatingRowBackground"
                                Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                        <Setter Property="GridLinesVisibility"
                                Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush"
                                Value="{DynamicResource DividerStrokeColorDefaultBrush}"/>
                        <Setter Property="VerticalGridLinesBrush"
                                Value="{DynamicResource DividerStrokeColorDefaultBrush}"/>
                </Style>

                <!-- 自定义DataGrid行样式 - 优化选中效果 -->
                <Style x:Key="CustomDataGridRowStyle"
                       TargetType="DataGridRow">
                        <Setter Property="Background"
                                Value="Transparent"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                        <Style.Triggers>
                                <!-- 鼠标悬停效果 -->
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                                </Trigger>
                                <!-- 选中效果 - 使用更柔和的颜色 -->
                                <Trigger Property="IsSelected"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                                        <Setter Property="Foreground"
                                                Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                </Trigger>
                                <!-- 选中且鼠标悬停 -->
                                <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                                <Condition Property="IsSelected"
                                                           Value="True"/>
                                                <Condition Property="IsMouseOver"
                                                           Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background"
                                                Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                                </MultiTrigger>
                        </Style.Triggers>
                </Style>

                <!-- 自定义DataGrid单元格样式 - 移除默认选中背景 -->
                <Style x:Key="CustomDataGridCellStyle"
                       TargetType="DataGridCell">
                        <Setter Property="Background"
                                Value="Transparent"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="8,4"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                        <Style.Triggers>
                                <!-- 移除默认的选中背景色 -->
                                <Trigger Property="IsSelected"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                        <Setter Property="Foreground"
                                                Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                </Trigger>
                                <!-- 获得焦点时也保持透明 -->
                                <Trigger Property="IsFocused"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题和工具栏 - 支持主题 -->
                <Grid Grid.Row="0"
                      Background="{DynamicResource LayerFillColorDefaultBrush}"
                      Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                   Text="任务历史记录"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Margin="15,10"/>

                        <StackPanel Grid.Column="2"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <ui:Button Content="导出CSV"
                                           Width="80"
                                           Margin="5,0"
                                           Command="{Binding ExportToCsvCommand}"/>
                                <ui:Button Content="刷新统计"
                                           Width="80"
                                           Margin="5,0"
                                           Command="{Binding RefreshStatisticsCommand}"/>
                        </StackPanel>
                </Grid>

                <!-- 统计卡片 -->
                <Grid Grid.Row="1"
                      Margin="10,0,10,10">
                        <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 总记录数 -->
                        <Border Grid.Column="0"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="总记录数"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Text="{Binding TotalRecords, Mode=OneWay}"
                                                   Style="{StaticResource StatValueStyle}"/>
                                </StackPanel>
                        </Border>

                        <!-- 成功记录 -->
                        <Border Grid.Column="1"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="成功任务"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Text="{Binding SuccessRecords, Mode=OneWay}"
                                                   Style="{StaticResource StatValueStyle}"
                                                   Foreground="Green"/>
                                </StackPanel>
                        </Border>

                        <!-- 失败记录 -->
                        <Border Grid.Column="2"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="失败任务"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Text="{Binding FailureRecords, Mode=OneWay}"
                                                   Style="{StaticResource StatValueStyle}"
                                                   Foreground="Red"/>
                                </StackPanel>
                        </Border>

                        <!-- 中断记录 -->
                        <Border Grid.Column="3"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="中断任务"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Text="{Binding CancelledRecords, Mode=OneWay}"
                                                   Style="{StaticResource StatValueStyle}"
                                                   Foreground="Orange"/>
                                </StackPanel>
                        </Border>

                        <!-- 执行中记录 -->
                        <Border Grid.Column="4"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="执行中任务"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Text="{Binding RunningRecords, Mode=OneWay}"
                                                   Style="{StaticResource StatValueStyle}"
                                                   Foreground="Blue"/>
                                </StackPanel>
                        </Border>

                        <!-- 成功率 -->
                        <Border Grid.Column="5"
                                Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                        <TextBlock Text="成功率"
                                                   Style="{StaticResource StatTitleStyle}"/>
                                        <TextBlock Style="{StaticResource StatValueStyle}"
                                                   Foreground="Green">
                        <Run Text="{Binding SuccessRate, StringFormat={}{0:F2}, Mode=OneWay}"/>
                        <Run Text="%"/>
                                        </TextBlock>
                                </StackPanel>
                        </Border>
                </Grid>

                <!-- 过滤条件面板 - 支持主题 -->
                <Border Grid.Row="2"
                        Background="{DynamicResource LayerFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="10,0,10,10"
                        Padding="10">
                        <Grid>
                                <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行：日期范围 -->
                                <Grid Grid.Row="0"
                                      Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                   Text="日期范围:"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,10,0"/>

                                        <StackPanel Grid.Column="1"
                                                    Orientation="Horizontal">
                                                <DatePicker SelectedDate="{Binding StartDate}"
                                                            Width="160"/>
                                                <TextBlock Text="至"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                           Margin="10,0"/>
                                                <DatePicker SelectedDate="{Binding EndDate}"
                                                            Width="160"/>
                                        </StackPanel>

                                        <ui:Button Grid.Column="2"
                                                   Content="应用筛选"
                                                   Command="{Binding ApplyFiltersCommand}"
                                                   Padding="15,3"
                                                   Appearance="Primary"/>
                                </Grid>

                                <!-- 第二行：模拟器、状态和搜索 -->
                                <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 模拟器选择 -->
                                        <TextBlock Grid.Column="0"
                                                   Text="模拟器:"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,0"/>
                                        <ComboBox Grid.Column="1"
                                                  ItemsSource="{Binding AvailableEmulators}"
                                                  SelectedItem="{Binding SelectedEmulator}"
                                                  Width="130"
                                                  Margin="0,0,15,0"/>

                                        <!-- 状态选择 -->
                                        <TextBlock Grid.Column="2"
                                                   Text="状态:"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,0"/>
                                        <ComboBox Grid.Column="3"
                                                  ItemsSource="{Binding AvailableStatuses}"
                                                  SelectedItem="{Binding SelectedStatus}"
                                                  Width="100"
                                                  Margin="0,0,15,0"/>

                                        <!-- 搜索框 -->
                                        <TextBlock Grid.Column="4"
                                                   Text="搜索:"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,0"/>
                                        <TextBox Grid.Column="5"
                                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                                 Margin="0,0,0,0">
                                                <TextBox.InputBindings>
                                                        <KeyBinding Key="Return"
                                                                    Command="{Binding ApplyFiltersCommand}"/>
                                                </TextBox.InputBindings>
                                        </TextBox>
                                </Grid>
                        </Grid>
                </Border>

                <!-- 历史记录列表 - 应用自定义样式 -->
                <Grid Grid.Row="3"
                      Margin="10,5">
                        <DataGrid ItemsSource="{Binding CurrentPageRecords}"
                                  SelectedItem="{Binding SelectedRecord}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  CanUserResizeColumns="True"
                                  CanUserSortColumns="True"
                                  Style="{StaticResource CustomDataGridStyle}"
                                  RowStyle="{StaticResource CustomDataGridRowStyle}"
                                  CellStyle="{StaticResource CustomDataGridCellStyle}"
                                  SelectionChanged="HistoryRecords_SelectionChanged">
                                <DataGrid.Columns>
                                        <DataGridTextColumn Header="时间"
                                                            Binding="{Binding StartTime, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}"
                                                            Width="160"/>
                                        <DataGridTextColumn Header="任务名称"
                                                            Binding="{Binding TaskName}"
                                                            Width="150"/>
                                        <DataGridTextColumn Header="模拟器"
                                                            Binding="{Binding EmulatorName}"
                                                            Width="100"/>
                                        <DataGridTextColumn Header="状态"
                                                            Binding="{Binding Status}"
                                                            Width="80">
                                                <DataGridTextColumn.CellStyle>
                                                        <Style TargetType="DataGridCell"
                                                               BasedOn="{StaticResource CustomDataGridCellStyle}">
                                                                <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding Status}"
                                                                                     Value="成功">
                                                                                <Setter Property="Foreground"
                                                                                        Value="#22C55E"/>
                                                                                <Setter Property="FontWeight"
                                                                                        Value="Bold"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Status}"
                                                                                     Value="失败">
                                                                                <Setter Property="Foreground"
                                                                                        Value="#EF4444"/>
                                                                                <Setter Property="FontWeight"
                                                                                        Value="Bold"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Status}"
                                                                                     Value="中断">
                                                                                <Setter Property="Foreground"
                                                                                        Value="#F59E0B"/>
                                                                                <Setter Property="FontWeight"
                                                                                        Value="Bold"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Status}"
                                                                                     Value="执行中">
                                                                                <Setter Property="Foreground"
                                                                                        Value="#3B82F6"/>
                                                                                <Setter Property="FontWeight"
                                                                                        Value="Bold"/>
                                                                        </DataTrigger>
                                                                </Style.Triggers>
                                                        </Style>
                                                </DataGridTextColumn.CellStyle>
                                        </DataGridTextColumn>
                                        <DataGridTextColumn Header="持续时间"
                                                            Binding="{Binding FormattedDuration}"
                                                            Width="120"/>
                                        <DataGridTextColumn Header="调度类型"
                                                            Binding="{Binding ScheduleType}"
                                                            Width="80"/>
                                        <DataGridTextColumn Header="任务类型"
                                                            Binding="{Binding TaskType}"
                                                            Width="100"/>
                                        <DataGridTextColumn Header="重试次数"
                                                            Binding="{Binding RetryCount}"
                                                            Width="80"/>
                                        <DataGridTextColumn Header="错误信息"
                                                            Binding="{Binding ErrorMessage}"
                                                            Width="*"/>
                                </DataGrid.Columns>
                        </DataGrid>

                        <!-- 加载遮罩 - 支持主题 -->
                        <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                              Background="{DynamicResource SolidBackgroundFillColorBaseBrush}">
                                <StackPanel VerticalAlignment="Center"
                                            HorizontalAlignment="Center">
                                        <TextBlock Text="正在加载..."
                                                   FontSize="16"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   HorizontalAlignment="Center"/>
                                        <ProgressBar IsIndeterminate="True"
                                                     Width="200"
                                                     Height="10"
                                                     Margin="0,10,0,0"/>
                                </StackPanel>
                        </Grid>
                </Grid>

                <!-- 详细信息面板 - 支持主题 -->
                <Expander Grid.Row="3"
                          VerticalAlignment="Bottom"
                          Header="任务详细信息"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                          Margin="10,0,10,5"
                          Visibility="{Binding SelectedRecord, Converter={StaticResource NullToVisibilityConverter}}">
                        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="5"
                                Padding="10">
                                <Grid>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 第一行 -->
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="任务ID:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   Text="{Binding SelectedRecord.TaskId}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,10,5"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="2"
                                                   Text="记录ID:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="3"
                                                   Text="{Binding SelectedRecord.Id}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,5"/>

                                        <!-- 第二行 -->
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="0"
                                                   Text="任务名称:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="1"
                                                   Text="{Binding SelectedRecord.TaskName}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,10,5"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="2"
                                                   Text="模拟器:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="3"
                                                   Text="{Binding SelectedRecord.EmulatorName}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,5"/>

                                        <!-- 第三行 -->
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="0"
                                                   Text="开始时间:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="1"
                                                   Text="{Binding SelectedRecord.StartTime, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,10,5"/>
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="2"
                                                   Text="结束时间:"
                                                   FontWeight="Bold"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                   Margin="0,0,5,5"/>
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="3"
                                                   Text="{Binding SelectedRecord.EndTime, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,5"/>

                                        <!-- 错误信息 - 支持主题 -->
                                        <Border Grid.Row="3"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="4"
                                                Background="{DynamicResource SystemFillColorCriticalBackgroundBrush}"
                                                BorderBrush="{DynamicResource SystemFillColorCriticalBrush}"
                                                BorderThickness="1"
                                                CornerRadius="3"
                                                Margin="0,5,0,0"
                                                Padding="5"
                                                Visibility="{Binding SelectedRecord.ErrorMessage, Converter={StaticResource NullToVisibilityConverter}}">
                                                <StackPanel>
                                                        <TextBlock Text="错误信息:"
                                                                   FontWeight="Bold"
                                                                   Foreground="{DynamicResource SystemFillColorCriticalBrush}"/>
                                                        <TextBlock Text="{Binding SelectedRecord.ErrorMessage}"
                                                                   TextWrapping="Wrap"
                                                                   Foreground="{DynamicResource SystemFillColorCriticalBrush}"/>
                                                </StackPanel>
                                        </Border>
                                </Grid>
                        </Border>
                </Expander>

                <!-- 底部状态栏 - 支持主题 -->
                <Grid Grid.Row="4"
                      Background="{DynamicResource LayerFillColorDefaultBrush}">
                        <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <TextBlock VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="0,0,15,0">
                                        <Run Text="显示"/>
                                        <Run Text="{Binding CurrentPageRecords.Count, Mode=OneWay}"/>
                                        <Run Text="/"/>
                                        <Run Text="{Binding FilteredRecords.Count, Mode=OneWay}"/>
                                        <Run Text="条记录"/>
                                </TextBlock>

                                <!-- 分页控制 -->
                                <Button Content="首页"
                                        Margin="5,0"
                                        Command="{Binding FirstPageCommand}"
                                        IsEnabled="{Binding CurrentPage, Converter={StaticResource GreaterThanOneConverter}}"/>
                                <Button Content="上一页"
                                        Margin="5,0"
                                        Command="{Binding PreviousPageCommand}"
                                        IsEnabled="{Binding CurrentPage, Converter={StaticResource GreaterThanOneConverter}}"/>
                                <TextBlock VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="5,0">
                                    <Run Text="第"/>
                                    <Run Text="{Binding CurrentPage}"/>
                                    <Run Text="/"/>
                                    <Run Text="{Binding TotalPages}"/>
                                    <Run Text="页"/>
                                </TextBlock>
                                <Button Content="下一页"
                                        Margin="5,0"
                                        Command="{Binding NextPageCommand}"
                                        IsEnabled="{Binding CanGoToNextPage}"/>
                                <Button Content="末页"
                                        Margin="5,0"
                                        Command="{Binding LastPageCommand}"
                                        IsEnabled="{Binding CanGoToNextPage}"/>
                                <TextBlock Text="每页显示:"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="10,0,5,0"/>
                                <ComboBox SelectedValue="{Binding PageSize}"
                                          SelectedValuePath="Content">
                                        <ComboBoxItem Content="20"/>
                                        <ComboBoxItem Content="50"/>
                                        <ComboBoxItem Content="100"/>
                                        <ComboBoxItem Content="200"/>
                                </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="1"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <CheckBox Content="自动刷新"
                                          IsChecked="{Binding IsAutoRefreshEnabled}"
                                          VerticalAlignment="Center"
                                          Margin="0,0,5,0"/>
                                <TextBlock Text="间隔:"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="5,0,5,0"/>
                                <TextBox Text="{Binding RefreshInterval}"
                                         Width="40"
                                         VerticalAlignment="Center"/>
                                <TextBlock Text="秒"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="5,0,10,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <TextBlock Text="保留历史天数:"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="0,0,5,0"/>
                                <TextBox Text="{Binding DaysToKeep}"
                                         Width="40"
                                         VerticalAlignment="Center"/>
                                <Button Content="清理历史"
                                        Width="80"
                                        Margin="10,0,0,0"
                                        Command="{Binding CleanupRecordsCommand}"/>
                        </StackPanel>
                </Grid>
        </Grid>
</Window> 