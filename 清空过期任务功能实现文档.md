# 清空过期任务功能实现文档

## 功能概述

在定时调度系统中实现了"清空过期任务"功能，允许用户一键清理所有过期的一次性任务，保持任务列表的整洁。

## 功能特点

### 🎯 **精确筛选**
- 只清理**一次性任务**且状态为**已过期**的任务
- 保留所有其他类型的任务（每日、每周、间隔执行等）
- 保留所有其他状态的任务（待执行、执行中、已完成等）

### 🔒 **安全确认**
- 执行前显示确认对话框
- 显示将要删除的任务数量和详细列表
- 用户可以取消操作

### 📊 **详细反馈**
- 显示操作结果和删除的任务数量
- 完整的日志记录
- 友好的用户提示信息

## 实现细节

### 1. XAML界面绑定

在 `SchedulerWindow.xaml` 中，将按钮绑定到命令：

```xml
<ui:Button
    Margin="5 0 0 0"
    ToolTip="清空当前所有已经过期的一次性任务..."
    HorizontalAlignment="Left"
    Appearance="Secondary"
    VerticalAlignment="Center"
    Command="{Binding ClearExpiredTasksCommand}"
    Padding="8,4">
    <StackPanel Orientation="Horizontal">
        <ui:SymbolIcon Symbol="TextGrammarDismiss24"
                       Margin="0 0 5 0" />
        <TextBlock Text="清空过期任务" />
    </StackPanel>
</ui:Button>
```

### 2. ViewModel命令实现

在 `SchedulerWindowViewModel.cs` 中实现 `ClearExpiredTasksCommand`：

```csharp
/// <summary>
/// 清空过期任务命令
/// </summary>
[RelayCommand]
private void ClearExpiredTasks()
{
    LogInfo("准备清空过期任务");

    try
    {
        // 查找所有过期的一次性任务
        var expiredTasks = ScheduledTasks
            .Where(t => t.ScheduleType == "一次性" && t.Status == "已过期")
            .ToList();

        if (expiredTasks.Count == 0)
        {
            LogInfo("没有找到过期的一次性任务");
            MessageBox.Show("没有找到过期的一次性任务。", "清空过期任务", 
                MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 显示确认对话框
        string confirmMessage = $"确定要清空 {expiredTasks.Count} 个过期的一次性任务吗？\n\n" +
                              "过期任务列表：\n" +
                              string.Join("\n", expiredTasks.Take(5).Select(t => $"• {t.Name} ({t.EmulatorName})")) +
                              (expiredTasks.Count > 5 ? $"\n... 还有 {expiredTasks.Count - 5} 个任务" : "");

        var result = MessageBox.Show(confirmMessage, "确认清空过期任务", 
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result != MessageBoxResult.Yes)
        {
            LogInfo("用户取消了清空过期任务操作");
            return;
        }

        // 执行清空操作
        int removedCount = 0;
        foreach (var task in expiredTasks)
        {
            ScheduledTasks.Remove(task);
            removedCount++;
            LogInfo($"已删除过期任务: {task.Name} ({task.EmulatorName})");
        }

        // 保存任务配置
        SaveTasksConfigToFile();

        // 更新过滤后的任务列表
        UpdateFilteredTasks();

        // 更新模拟器的下一个任务信息
        UpdateEmulatorsNextTask();

        LogInfo($"成功清空 {removedCount} 个过期的一次性任务");
        MessageBox.Show($"成功清空 {removedCount} 个过期的一次性任务。", "清空完成", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }
    catch (Exception ex)
    {
        LogError($"清空过期任务失败: {ex.Message}");
        MessageBox.Show($"清空过期任务失败: {ex.Message}", "错误", 
            MessageBoxButton.OK, MessageBoxImage.Error);
    }
}
```

## 功能流程

### 1. 任务筛选
```csharp
var expiredTasks = ScheduledTasks
    .Where(t => t.ScheduleType == "一次性" && t.Status == "已过期")
    .ToList();
```

**筛选条件**：
- `ScheduleType == "一次性"` - 只处理一次性任务
- `Status == "已过期"` - 只处理已过期的任务

### 2. 用户确认
- 如果没有找到过期任务，显示信息提示
- 如果找到过期任务，显示确认对话框
- 对话框包含：
  - 过期任务数量
  - 前5个任务的详细信息
  - 如果超过5个，显示剩余数量

### 3. 执行清理
- 遍历所有过期的一次性任务
- 从 `ScheduledTasks` 集合中移除
- 记录每个删除的任务
- 保存配置文件
- 更新UI显示

### 4. 结果反馈
- 显示成功删除的任务数量
- 记录详细的操作日志
- 更新相关UI组件

## 测试验证

创建了完整的单元测试 `ClearExpiredTasksTests.cs`：

1. **TestClearExpiredTasks_WithExpiredTasks** - 测试有过期任务的情况
2. **TestClearExpiredTasks_NoExpiredTasks** - 测试没有过期任务的情况
3. **TestClearExpiredTasks_OnlyExpiredOneTimeTasks** - 测试只有过期一次性任务的情况
4. **TestExpiredTaskFiltering** - 测试筛选逻辑的准确性

## MVVM架构遵循

### ✅ **View层**
- 只负责UI展示和用户交互
- 通过数据绑定连接ViewModel
- 不包含业务逻辑

### ✅ **ViewModel层**
- 包含所有业务逻辑
- 使用 `[RelayCommand]` 特性实现命令
- 通过属性绑定更新UI
- 处理异常和日志记录

### ✅ **Model层**
- `ScheduledTask` 模型定义任务结构
- 数据持久化通过配置文件

## 用户体验

### 🎨 **界面友好**
- 清晰的按钮图标和提示文本
- 一致的UI风格
- 合适的按钮位置

### 🔔 **操作反馈**
- 操作前确认
- 操作中日志记录
- 操作后结果提示

### 🛡️ **安全保护**
- 二次确认防止误操作
- 详细的任务列表预览
- 完整的错误处理

## 扩展性

该功能设计具有良好的扩展性：

1. **筛选条件可扩展** - 可以轻松修改筛选逻辑
2. **操作类型可扩展** - 可以添加其他批量操作
3. **确认方式可扩展** - 可以自定义确认对话框
4. **日志记录可扩展** - 可以添加更详细的操作记录

## 注意事项

1. **数据安全** - 删除操作不可逆，需要用户确认
2. **性能考虑** - 大量任务时的筛选和删除性能
3. **并发安全** - 确保UI线程安全操作
4. **配置同步** - 删除后及时保存配置文件
