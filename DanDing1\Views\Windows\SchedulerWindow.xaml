<Window x:Class="DanDing1.Views.Windows.SchedulerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:localControl="clr-namespace:DanDing1.Views.UserControls"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:viewModels="clr-namespace:DanDing1.ViewModels.Windows"
        x:Name="SchedulerWindowInstance"
        Title="{Binding ApplicationTitle}"
        Height="910"
        Width="1400"
        d:DataContext="{d:DesignInstance viewModels:SchedulerWindowViewModel, IsDesignTimeCreatable=False}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        Closing="Window_Closing"
        Loaded="Window_Loaded"
        mc:Ignorable="d">
    <Window.Resources>
        <!-- BooleanToVisibilityConverter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- NullToVisibilityConverter -->
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="8"/>
            <Setter Property="Padding"
                    Value="12"/>
            <Setter Property="Margin"
                    Value="0,0,0,10"/>
        </Style>

        <!-- 卡片标题样式 -->
        <Style x:Key="CardTitleStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="16"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Margin"
                    Value="0,0,0,8"/>
        </Style>
    </Window.Resources>

    <!-- 主布局分为左右两部分 -->
    <Grid x:Name="MainGrid">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"
                              MinWidth="360"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"
                              MinWidth="300"
                              MaxWidth="450"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧区域 - 包含定时任务列表、模拟器配置和日志 -->
        <ScrollViewer Grid.Column="0"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <!-- 定时任务卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- 标题栏和添加按钮 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0"
                                       Margin="0 3 0 0"
                                       Text="定时任务列表"
                                       Style="{StaticResource CardTitleStyle}"/>

                            <!-- 显示当前选中的模拟器名称 -->
                            <TextBlock Grid.Column="1"
                                       Margin="0 3 10 0"
                                       VerticalAlignment="Center">
                                <Run Text="- 当前模拟器:"
                                     Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                <Run Text="{Binding CurrentEmulatorName, Mode=OneWay}"
                                     FontWeight="SemiBold"/>
                            </TextBlock>

                            <StackPanel Grid.Column="2"
                                        Orientation="Horizontal"
                                        HorizontalAlignment="Left">
                                <ui:Button
                                    Margin="0 0 5 0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    ToolTip="添加定时任务"
                                    Appearance="Primary"
                                    Command="{Binding AddScheduledTaskCommand}"
                                    Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Add24"
                                                       Margin="0 0 5 0"/>
                                        <TextBlock Text="添加任务"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    ToolTip="刷新任务列表"
                                    Appearance="Secondary"
                                    Command="{Binding RefreshTasksCommand}"
                                    Padding="8,4">
                                    <ui:SymbolIcon Symbol="ArrowClockwise24"/>
                                </ui:Button>
                                <ui:Button
                                    Margin="5 0 0 0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    ToolTip="显示所有模拟器的所有任务"
                                    Appearance="Secondary"
                                    Command="{Binding ShowAllTasksCommand}"
                                    Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="List24"
                                                       Margin="0 0 5 0"/>
                                        <TextBlock Text="显示全部任务"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button
                                    Margin="5 0 0 0"
                                    ToolTip="清空当前所有已经过期的一次性任务..."
                                    HorizontalAlignment="Left"
                                    Appearance="Secondary"
                                    VerticalAlignment="Center"
                                    Command="{Binding ClearExpiredTasksCommand}"
                                    Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="TextGrammarDismiss24"
                                                       Margin="0 0 5 0"/>
                                        <TextBlock Text="清空过期任务"/>
                                    </StackPanel>
                                </ui:Button>
                            </StackPanel>
                        </Grid>

                        <!-- 定时任务数据表格 -->
                        <ui:DataGrid x:Name="TasksDataGrid"
                                     Margin="0 3 0 0"
                                     Height="300"
                                     ItemsSource="{Binding FilteredTasks, Mode=TwoWay}"
                                     SelectionMode="Single"
                                     SelectedItem="{Binding SelectedTask, Mode=TwoWay}"
                                     SelectionChanged="TasksDataGrid_SelectionChanged"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     PreviewMouseRightButtonDown="TasksDataGrid_PreviewMouseRightButtonDown"
                                     CanUserReorderColumns="False">
                            <ui:DataGrid.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="编辑任务"
                                              Command="{Binding EditSelectedTaskCommand}"
                                              CommandParameter="{Binding SelectedTask}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Edit24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="删除任务"
                                              Command="{Binding DeleteSelectedTaskCommand}"
                                              CommandParameter="{Binding SelectedTask}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Delete24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="启用/禁用"
                                              Command="{Binding ToggleTaskEnabledCommand}"
                                              CommandParameter="{Binding SelectedTask}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="CheckboxChecked24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </ui:DataGrid.ContextMenu>

                            <ui:DataGrid.Columns>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="ID"
                                                    Binding="{Binding Id}"
                                                    Width="70"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="任务名称"
                                                    Binding="{Binding Name}"
                                                    Width="120"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="模拟器"
                                                    Binding="{Binding EmulatorName}"
                                                    Width="100"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="调度类型"
                                                    Binding="{Binding ScheduleType}"
                                                    Width="80"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="下次执行时间"
                                                    Binding="{Binding NextExecutionTime}"
                                                    Width="180"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="状态"
                                                    Binding="{Binding Status}"
                                                    Width="80"/>
                                <DataGridCheckBoxColumn IsReadOnly="False"
                                                        Header="启用"
                                                        Binding="{Binding Enabled, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                        Width="60"/>
                                <DataGridTemplateColumn Header="操作"
                                                        Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <ui:Button Appearance="Secondary"
                                                           Padding="4,2"
                                                           Margin="2,0"
                                                           Command="{Binding DataContext.EditSelectedTaskCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}">
                                                    <ui:SymbolIcon Symbol="Edit16"/>
                                                </ui:Button>
                                                <ui:Button Appearance="Danger"
                                                           Padding="4,2"
                                                           Margin="2,0"
                                                           Command="{Binding DataContext.DeleteSelectedTaskCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}">
                                                    <ui:SymbolIcon Symbol="Delete16"/>
                                                </ui:Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </ui:DataGrid.Columns>
                        </ui:DataGrid>
                    </StackPanel>
                </Border>

                <!-- 模拟器配置卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- 标题栏和添加按钮 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0"
                                       Margin="0 3 0 0"
                                       Text="模拟器配置"
                                       Style="{StaticResource CardTitleStyle}"/>
                            <StackPanel Grid.Column="1"
                                        Orientation="Horizontal"
                                        HorizontalAlignment="Left">
                                <ui:Button
                                    Margin="0 0 5 0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    ToolTip="添加模拟器"
                                    Appearance="Primary"
                                    Command="{Binding AddEmulatorCommand}"
                                    Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Add24"
                                                       Margin="0 0 5 0"/>
                                        <TextBlock Text="添加模拟器"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    ToolTip="刷新模拟器列表"
                                    Appearance="Secondary"
                                    Command="{Binding RefreshEmulatorsCommand}"
                                    Padding="8,4">
                                    <ui:SymbolIcon Symbol="ArrowClockwise24"/>
                                </ui:Button>
                            </StackPanel>
                        </Grid>

                        <!-- 模拟器数据表格 -->
                        <ui:DataGrid x:Name="EmulatorsDataGrid"
                                     Margin="0 3 0 0"
                                     Height="200"
                                     ItemsSource="{Binding Emulators, Mode=TwoWay}"
                                     SelectionMode="Single"
                                     SelectedItem="{Binding SelectedEmulator, Mode=TwoWay}"
                                     SelectionChanged="EmulatorsDataGrid_SelectionChanged"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     CanUserReorderColumns="False">
                            <ui:DataGrid.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="配置游戏设置"
                                              Command="{Binding EditEmulatorConfigCommand}"
                                              CommandParameter="{Binding SelectedEmulator}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Settings24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="删除模拟器"
                                              Command="{Binding DeleteSelectedEmulatorCommand}"
                                              CommandParameter="{Binding SelectedEmulator}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Delete24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </ui:DataGrid.ContextMenu>

                            <ui:DataGrid.Columns>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="ID"
                                                    Binding="{Binding Id}"
                                                    Width="45"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="模拟器名称"
                                                    Binding="{Binding Name}"
                                                    Width="125"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="状态"
                                                    Binding="{Binding Status}"
                                                    Width="80"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="当前任务"
                                                    Binding="{Binding CurrentTask}"
                                                    Width="120"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="下一个任务"
                                                    Binding="{Binding NextTask}"
                                                    Width="140"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="运行时长"
                                                    Binding="{Binding RunningDuration}"
                                                    Width="100"/>
                                <DataGridCheckBoxColumn IsReadOnly="False"
                                                        Header="启用"
                                                        Binding="{Binding Enabled, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                        Width="60"/>
                                <DataGridTemplateColumn Header="操作"
                                                        Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <ui:Button Appearance="Secondary"
                                                           Padding="4,2"
                                                           Margin="2,0"
                                                           ToolTip="编辑模拟器配置"
                                                           Command="{Binding DataContext.EditEmulatorConfigCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}">
                                                    <ui:SymbolIcon Symbol="Edit16"/>
                                                </ui:Button>
                                                <ui:Button Appearance="Danger"
                                                           Padding="4,2"
                                                           Margin="2,0"
                                                           ToolTip="删除模拟器"
                                                           Command="{Binding DataContext.DeleteSelectedEmulatorCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}">
                                                    <ui:SymbolIcon Symbol="Delete16"/>
                                                </ui:Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </ui:DataGrid.Columns>
                        </ui:DataGrid>
                    </StackPanel>
                </Border>

                <!-- 日志面板 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="系统日志"
                                   Style="{StaticResource CardTitleStyle}"/>
                        <ui:TextBox x:Name="MainLogTextBox"
                                    Text="{Binding MainLog, Mode=OneWay}"
                                    IsReadOnly="True"
                                    Height="250"
                                    TextWrapping="Wrap"
                                    VerticalScrollBarVisibility="Auto"
                                    BorderThickness="0"
                                    Padding="5"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- 分隔线 -->
        <GridSplitter Grid.Column="1"
                      Width="5"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Stretch"
                      Background="{DynamicResource ControlStrokeColorDefaultBrush}"/>

        <!-- 右侧区域 - 系统状态和配置 -->
        <Grid Grid.Column="2">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10"
                            HorizontalAlignment="Center"
                            MaxWidth="430">
                    <!-- 系统状态卡片 -->
                    <ui:CardExpander Icon="{ui:SymbolIcon DataArea24}"
                                     Margin="0,2,0,0"
                                     Width="430"
                                     IsExpanded="True">
                        <ui:CardExpander.Header>
                            <StackPanel Orientation="Horizontal">
                                <ui:TextBlock
                                    FontWeight="Black"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    Text="系统状态"/>
                            </StackPanel>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0"
                                           Grid.Column="0"
                                           Text="调度器状态:"
                                           Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0"
                                           Grid.Column="1"
                                           Text="{Binding SchedulerStatus}"
                                           Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1"
                                           Grid.Column="0"
                                           Text="活跃模拟器:"
                                           Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1"
                                           Grid.Column="1"
                                           Text="{Binding ActiveEmulators}"
                                           Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2"
                                           Grid.Column="0"
                                           Text="CPU使用率:"
                                           Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2"
                                           Grid.Column="1"
                                           Text="{Binding CpuUsage}"
                                           Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="3"
                                           Grid.Column="0"
                                           Text="内存使用率:"
                                           Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="3"
                                           Grid.Column="1"
                                           Text="{Binding MemoryUsage}"
                                           Margin="0,0,0,5"/>
                            </Grid>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 系统设置卡片 -->
                    <ui:CardExpander Icon="{ui:SymbolIcon Settings24}"
                                     Margin="0,2,0,0"
                                     Width="430"
                                     IsExpanded="True">
                        <ui:CardExpander.Header>
                            <StackPanel Orientation="Horizontal">
                                <ui:TextBlock
                                    FontWeight="Black"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    Text="系统设置"/>
                            </StackPanel>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <!-- 调度器资源设置组 -->
                            <ui:CardExpander Icon="{ui:SymbolIcon Server24}"
                                             Margin="0,2,0,0"
                                             IsExpanded="True">
                                <ui:CardExpander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <ui:TextBlock
                                            FontWeight="Black"
                                            VerticalAlignment="Center"
                                            FontSize="15"
                                            Text="调度器资源设置"/>
                                    </StackPanel>
                                </ui:CardExpander.Header>
                                <Grid Margin="8,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0"
                                               Grid.Column="0"
                                               Text="最大并发模拟器:"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:NumberBox Grid.Row="0"
                                                  Grid.Column="1"
                                                  Value="{Binding MaxConcurrentEmulators, Mode=TwoWay}"
                                                  Margin="0,0,0,10"
                                                  Minimum="1"
                                                  Maximum="10"/>

                                    <TextBlock Grid.Row="1"
                                               Grid.Column="0"
                                               Text="默认任务超时(秒):"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:NumberBox Grid.Row="1"
                                                  Grid.Column="1"
                                                  Value="{Binding DefaultTaskTimeout, Mode=TwoWay}"
                                                  Margin="0,0,0,10"
                                                  Minimum="60"
                                                  Maximum="86400"/>
                                </Grid>
                            </ui:CardExpander>

                            <!-- 空闲控制设置组 -->
                            <ui:CardExpander Icon="{ui:SymbolIcon Clock24}"
                                             Margin="0,2,0,0">
                                <ui:CardExpander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <ui:TextBlock
                                            FontWeight="Black"
                                            VerticalAlignment="Center"
                                            FontSize="15"
                                            Text="空闲控制设置"/>
                                    </StackPanel>
                                </ui:CardExpander.Header>
                                <Grid Margin="8,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0"
                                               Grid.Column="0"
                                               Text="启用空闲自动关闭:"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.Column="1"
                                                     IsChecked="{Binding IdleShutdownEnabled, Mode=TwoWay}"
                                                     Margin="0,0,0,10"/>

                                    <TextBlock Grid.Row="1"
                                               Grid.Column="0"
                                               Text="空闲自动关闭(秒):"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:NumberBox Grid.Row="1"
                                                  Grid.Column="1"
                                                  Value="{Binding AutoShutdownIdleTime, Mode=TwoWay}"
                                                  Margin="0,0,0,10"
                                                  Minimum="0"
                                                  Maximum="3600"
                                                  IsEnabled="{Binding IdleShutdownEnabled}"/>
                                </Grid>
                            </ui:CardExpander>

                            <!-- 任务重试设置组 -->
                            <ui:CardExpander Icon="{ui:SymbolIcon ArrowSync24}"
                                             Margin="0,2,0,0">
                                <ui:CardExpander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <ui:TextBlock
                                            FontWeight="Black"
                                            VerticalAlignment="Center"
                                            FontSize="15"
                                            Text="任务重试设置"/>
                                    </StackPanel>
                                </ui:CardExpander.Header>
                                <Grid Margin="8,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0"
                                               Grid.Column="0"
                                               Text="启用任务失败重试:"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.Column="1"
                                                     IsChecked="{Binding EnableTaskRetry, Mode=TwoWay}"
                                                     Margin="0,0,0,10"/>

                                    <TextBlock Grid.Row="1"
                                               Grid.Column="0"
                                               Text="最大重试次数:"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:NumberBox Grid.Row="1"
                                                  Grid.Column="1"
                                                  Value="{Binding MaxRetryCount, Mode=TwoWay}"
                                                  Margin="0,0,0,10"
                                                  Minimum="1"
                                                  Maximum="10"
                                                  IsEnabled="{Binding EnableTaskRetry}"/>
                                </Grid>
                            </ui:CardExpander>

                            <!-- 自动启动设置组 -->
                            <ui:CardExpander Icon="{ui:SymbolIcon Play24}"
                                             Margin="0,2,0,0">
                                <ui:CardExpander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <ui:TextBlock
                                            FontWeight="Black"
                                            VerticalAlignment="Center"
                                            FontSize="15"
                                            Text="自动启动设置"/>
                                    </StackPanel>
                                </ui:CardExpander.Header>
                                <Grid Margin="8,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0"
                                               Grid.Column="0"
                                               Text="自动开启调度器:"
                                               Margin="0,0,10,10"
                                               VerticalAlignment="Center"/>
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.Column="1"
                                                     IsChecked="{Binding AutoStartScheduler, Mode=TwoWay}"
                                                     Margin="0,0,0,10"/>
                                </Grid>
                            </ui:CardExpander>

                            <!-- 应用设置按钮 -->
                            <StackPanel HorizontalAlignment="Right"
                                        Orientation="Horizontal"
                                        Margin="0,10,0,0">
                                <ui:Button
                                    Content="恢复默认"
                                    Appearance="Secondary"
                                    Command="{Binding RestoreDefaultSettingsCommand}"
                                    Margin="0,0,10,0"/>
                                <ui:Button
                                    Content="应用设置"
                                    Appearance="Primary"
                                    Command="{Binding SaveSettingsCommand}"/>
                            </StackPanel>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 控制按钮卡片 -->
                    <ui:CardExpander Icon="{ui:SymbolIcon ControlButton24}"
                                     Margin="0,2,0,0"
                                     Width="430"
                                     IsExpanded="True">
                        <ui:CardExpander.Header>
                            <StackPanel Orientation="Horizontal">
                                <ui:TextBlock
                                    FontWeight="Black"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    Text="系统控制"/>
                            </StackPanel>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行按钮 -->
                                <ui:Button Grid.Row="0"
                                           Grid.Column="0"
                                           Margin="5,5,5,10"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Center"
                                           Command="{Binding StartSchedulerSyncCommand}"
                                           IsEnabled="{Binding CanStartScheduler}">
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <ui:SymbolIcon Symbol="Play24"
                                                       Margin="0,0,5,0"/>
                                        <TextBlock Text="启动调度器"/>
                                    </StackPanel>
                                </ui:Button>

                                <ui:Button Grid.Row="0"
                                           Grid.Column="1"
                                           Margin="5,5,5,10"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Center"
                                           Command="{Binding StopSchedulerCommand}"
                                           IsEnabled="{Binding CanStopScheduler}">
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <ui:SymbolIcon Symbol="Stop24"
                                                       Margin="0,0,5,0"/>
                                        <TextBlock Text="停止调度器"/>
                                    </StackPanel>
                                </ui:Button>

                                <!-- 第二行按钮：简化的导入导出 -->
                                <ui:Button Grid.Row="1"
                                           Grid.Column="0"
                                           Margin="5,0,5,5"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Center"
                                           Command="{Binding ImportMergedConfigCommand}">
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <ui:SymbolIcon Symbol="ArrowDownload24"
                                                       Margin="0,0,5,0"/>
                                        <TextBlock Text="导入配置"/>
                                    </StackPanel>
                                </ui:Button>

                                <ui:Button Grid.Row="1"
                                           Grid.Column="1"
                                           Margin="5,0,5,5"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Center"
                                           Command="{Binding ExportMergedConfigCommand}">
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <ui:SymbolIcon Symbol="ArrowUpload24"
                                                       Margin="0,0,5,0"/>
                                        <TextBlock Text="导出配置"/>
                                    </StackPanel>
                                </ui:Button>

                                <!-- 第三行按钮：历史记录 -->
                                <ui:Button Grid.Row="2"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2"
                                           Margin="5,10,5,5"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Center"
                                           Command="{Binding OpenTaskHistoryCommand}">
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <ui:SymbolIcon Symbol="History24"
                                                       Margin="0,0,5,0"/>
                                        <TextBlock Text="查看任务历史记录"/>
                                    </StackPanel>
                                </ui:Button>
                            </Grid>
                        </StackPanel>
                    </ui:CardExpander>
                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- 加载遮罩层 -->
        <Border Grid.ColumnSpan="3"
                Background="#80000000"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center"
                        HorizontalAlignment="Center">
                <ui:ProgressRing IsIndeterminate="True"
                                 Width="60"
                                 Height="60"
                                 Foreground="White"/>
                <TextBlock Text="{Binding LoadingMessage}"
                           FontSize="16"
                           Margin="0,20,0,0"
                           Foreground="White"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 