﻿#pragma checksum "..\..\..\..\..\Views\Pages\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6BEB18FBD8EFEB162504D642724448CB395E7479"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Helpers;
using DanDing1.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Pages {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button MultipleInstanceNameButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartSaveTasks;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartOpenLog;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OpenLogItems;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecordQuality;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button ParseSRFile;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button MaterialCollector;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button OpenMuMuConfig;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button OpenAppFolder;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button CreateShortcut;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ServerHost;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServerHost_Tip;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PicServer;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PicServerVer;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button DownloadPicServer;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowDebug;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button InstallPaddleOCR;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button CheckUpdateButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/pages/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MultipleInstanceNameButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 56 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.MultipleInstanceNameButton.Click += new System.Windows.RoutedEventHandler(this.MultipleInstanceNameButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StartSaveTasks = ((System.Windows.Controls.CheckBox)(target));
            
            #line 62 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.StartSaveTasks.Checked += new System.Windows.RoutedEventHandler(this.StartSaveTasks_Checked);
            
            #line default
            #line hidden
            
            #line 63 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.StartSaveTasks.Unchecked += new System.Windows.RoutedEventHandler(this.StartSaveTasks_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.StartOpenLog = ((System.Windows.Controls.CheckBox)(target));
            
            #line 69 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.StartOpenLog.Checked += new System.Windows.RoutedEventHandler(this.StartOpenLog_Checked);
            
            #line default
            #line hidden
            
            #line 71 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.StartOpenLog.Unchecked += new System.Windows.RoutedEventHandler(this.StartOpenLog_Checked);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OpenLogItems = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.RecordQuality = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.ParseSRFile = ((Wpf.Ui.Controls.Button)(target));
            
            #line 88 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.ParseSRFile.Click += new System.Windows.RoutedEventHandler(this.ParseSRFile_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.MaterialCollector = ((Wpf.Ui.Controls.Button)(target));
            
            #line 96 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.MaterialCollector.Click += new System.Windows.RoutedEventHandler(this.MaterialCollector_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.OpenMuMuConfig = ((Wpf.Ui.Controls.Button)(target));
            
            #line 109 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.OpenMuMuConfig.Click += new System.Windows.RoutedEventHandler(this.OpenMuMuConfig_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.OpenAppFolder = ((Wpf.Ui.Controls.Button)(target));
            
            #line 146 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.OpenAppFolder.Click += new System.Windows.RoutedEventHandler(this.OpenAppFolder_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CreateShortcut = ((Wpf.Ui.Controls.Button)(target));
            
            #line 151 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.CreateShortcut.Click += new System.Windows.RoutedEventHandler(this.CreateShortcut_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ServerHost = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.ServerHost_Tip = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.PicServer = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.PicServerVer = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.DownloadPicServer = ((Wpf.Ui.Controls.Button)(target));
            
            #line 185 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.DownloadPicServer.Click += new System.Windows.RoutedEventHandler(this.DownloadPicServer_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ShowDebug = ((System.Windows.Controls.CheckBox)(target));
            
            #line 190 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.ShowDebug.Checked += new System.Windows.RoutedEventHandler(this.ShowDebug_Checked);
            
            #line default
            #line hidden
            
            #line 192 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.ShowDebug.Unchecked += new System.Windows.RoutedEventHandler(this.ShowDebug_Checked);
            
            #line default
            #line hidden
            return;
            case 17:
            this.InstallPaddleOCR = ((Wpf.Ui.Controls.Button)(target));
            
            #line 235 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.InstallPaddleOCR.Click += new System.Windows.RoutedEventHandler(this.InstallPaddleOCR_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 259 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.CopyMachineCode_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 272 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.TextBlock_MouseDown);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CheckUpdateButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 280 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.CheckUpdateButton.Click += new System.Windows.RoutedEventHandler(this.CheckUpdateButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

