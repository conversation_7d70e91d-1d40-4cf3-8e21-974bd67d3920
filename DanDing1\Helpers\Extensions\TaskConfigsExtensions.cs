﻿using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Helpers.Extensions
{
    internal static class TaskConfigsExtensions
    {
        public static string GetScheduledTaskName(this ObservableCollection<TaskConfigsModel.Configs> values)
        {
            StringBuilder res = new();
            foreach (var item in values)
                res.Append(item.Name + item.Count + "|");

            if (res.Length > 0)
                res.Length--;

            return res.ToString();
        }

        public static string GetScheduledTaskType(this ObservableCollection<TaskConfigsModel.Configs> values)
        {
            StringBuilder res = new();
            foreach (var item in values)
                res.Append(item.Name + "|");

            if (res.Length > 0)
                res.Length--;

            return res.ToString();
        }

        /// <summary>
        /// 转为Json字符串
        /// </summary>
        /// <param name="values"></param>
        /// <returns></returns>
        public static string GetJsonString(this ObservableCollection<TaskConfigsModel.Configs> values)
        {
            if (values == null || values.Count == 0)
                return "[]";

            return System.Text.Json.JsonSerializer.Serialize(values, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
        }
    }
}