[00:21:12][DEBUG] CheckUserStatus|Running...
[00:21:12][DEBUG] CheckUserStatus|Ended|OK
[01:21:12][DEBUG] CheckUserStatus|Running...
[01:21:12][DEBUG] CheckUserStatus|Ended|OK
[02:21:12][DEBUG] CheckUserStatus|Running...
[02:21:12][DEBUG] CheckUserStatus|Ended|OK
[03:21:12][DEBUG] CheckUserStatus|Running...
[03:21:12][DEBUG] CheckUserStatus|Ended|OK
[04:21:12][DEBUG] CheckUserStatus|Running...
[04:21:12][DEBUG] CheckUserStatus|Ended|OK
[05:21:12][DEBUG] CheckUserStatus|Running...
[05:21:12][DEBUG] CheckUserStatus|Ended|OK
[06:21:12][DEBUG] CheckUserStatus|Running...
[06:21:12][DEBUG] CheckUserStatus|Ended|OK
[07:21:12][DEBUG] CheckUserStatus|Running...
[07:21:12][DEBUG] CheckUserStatus|Ended|OK
[08:21:12][DEBUG] CheckUserStatus|Running...
[08:21:13][DEBUG] CheckUserStatus|Ended|OK
[08:24:52][DEBUG] 已启动自动刷新，间隔：30秒
[08:24:52][DEBUG] 任务历史记录视图模型已初始化
[08:24:52][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共1条
[08:24:52][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录
[08:24:52][DEBUG] 应用过滤器后，显示1/1条记录
[08:24:52][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共1条
[08:24:52][DEBUG] 已刷新统计信息
[08:24:52][DEBUG] 任务历史记录视图模型已初始化完成
[08:25:04][DEBUG] 已停止自动刷新
[08:25:04][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止
[08:25:12][DEBUG] 已启动自动刷新，间隔：30秒
[08:25:12][DEBUG] 任务历史记录视图模型已初始化
[08:25:12][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共1条
[08:25:12][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录
[08:25:12][DEBUG] 应用过滤器后，显示1/1条记录
[08:25:12][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共1条
[08:25:12][DEBUG] 已刷新统计信息
[08:25:12][DEBUG] 任务历史记录视图模型已初始化完成
[08:25:16][DEBUG] 已停止自动刷新
[08:25:16][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止
[09:21:12][DEBUG] CheckUserStatus|Running...
[09:21:13][DEBUG] CheckUserStatus|Ended|OK
[09:33:15][DEBUG] 添加任务历史记录：日常任务1，ID：21c5e7b4-a21a-4a96-ab7a-e60ec31f26a7
[09:33:16][DEBUG] 已发送启动命令给模拟器，索引: 0，正在等待模拟器完全启动...
[09:33:17][DEBUG] 等待main_wnd就绪，索引: 0
[09:33:17][DEBUG] 等待render_wnd就绪，索引: 0
[09:33:20][DEBUG] 模拟器已完全启动成功，索引: 0，main_wnd和render_wnd均已就绪
[09:33:20][DEBUG] 成功解析窗口句柄 - 主窗口: 0xC50934, 渲染窗口: 0x1208F4
[09:33:29][DEBUG] 成功获取到模拟器(索引:0)的ADB端口: 16384
[09:33:29][DEBUG] 提供了ADB端口参数 16384，尝试自动连接
[09:33:29][DEBUG] ADB连接命令执行成功，正在检查设备状态...
[09:33:30][DEBUG] 设备 127.0.0.1:16384 状态: device
[09:33:30][DEBUG] 成功连接到ADB设备端口 16384，设备状态正常
[09:33:30][DEBUG] 设备 127.0.0.1:16384 状态: device
[09:33:31][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"

[09:33:37][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19
[09:33:37][DEBUG] Main_Pics 被更新！版本号：1.4.19
[09:33:37][DEBUG]  [定时任务(1b2ce)] 任务线程已经在后台执行，UI控制权转移给用户！
[09:33:37][DEBUG]  [定时任务(1b2ce)] 后台线程开启！
[09:33:42][DEBUG]  [定时任务(1b2ce)] 尝试使用绑定模式 0 绑定窗口
[09:33:44][DEBUG]  [定时任务(1b2ce)] 尝试使用绑定模式 0 绑定窗口
[09:33:45][DEBUG]  [定时任务(1b2ce)] dm_id已捕获：main[579116364] sub[579144764]
[09:33:48][DEBUG]  [定时任务(1b2ce)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭
[09:33:48][DEBUG]  [定时任务(1b2ce)] 小纸人配置 - 基础触发概率: 0.00%
[09:33:49][DEBUG]  [定时任务(1b2ce)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏
[09:34:03][DEBUG]  [定时任务(1b2ce)] 设置的庭院皮肤为：枫色秋庭
[09:34:10][DEBUG] 场景识别成功：庭院
[09:34:10][DEBUG]  [定时任务(1b2ce)] [TingYuan] 当前场景:庭院
[09:34:10][DEBUG] 场景识别成功：庭院
[09:34:18][DEBUG] 场景识别成功：庭院
[09:34:27][DEBUG] Ai预测模型加载完成！[Classify]
[09:34:27][DEBUG] YoloClassfly：未知
[09:34:27][DEBUG] AI场景识别成功：未知[YOLO]
[09:34:29][DEBUG] 场景识别成功：庭院
[09:34:33][DEBUG] 场景识别成功：庭院
[09:35:01][DEBUG] 场景识别成功：庭院
[09:35:01][DEBUG]  [定时任务(1b2ce)] [TingYuan] 当前场景:庭院
[09:35:01][DEBUG] 场景识别成功：庭院
[09:35:09][DEBUG] Tesseract识别结果：35
[09:35:36][DEBUG] 场景识别成功：庭院
[09:35:59][DEBUG] 场景识别成功：庭院
[09:36:03][DEBUG] 场景识别成功：阴阳寮
[09:36:30][DEBUG] 场景识别成功：庭院
[09:36:36][DEBUG] 场景识别成功：庭院
[09:36:36][DEBUG]  [定时任务(1b2ce)] [TanSuo] 当前场景:庭院
[09:36:36][DEBUG] 场景识别成功：庭院
[09:36:36][DEBUG] 场景识别成功：庭院
[09:36:42][DEBUG] 场景识别成功：探索
[09:36:42][DEBUG] 场景识别成功：探索
[09:37:01][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:37:15][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[09:37:17][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:37:31][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[09:37:33][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:37:37][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:37:52][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[09:37:54][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:38:08][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[09:38:10][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:38:23][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[09:38:26][DEBUG] 场景识别成功：探索
[09:38:36][DEBUG] 场景识别成功：探索
[09:39:10][DEBUG] 场景识别成功：探索
[09:39:19][DEBUG] 场景识别成功：探索
[09:39:54][DEBUG] 场景识别成功：探索
[09:40:03][DEBUG] 场景识别成功：探索
[09:40:20][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:40:34][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[09:40:36][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:40:50][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[09:40:52][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:41:06][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[09:41:08][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:41:22][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%
[09:41:30][DEBUG] 场景识别成功：探索
[09:41:40][DEBUG] 场景识别成功：探索
[09:41:57][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:42:11][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%
[09:42:13][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:42:27][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%
[09:42:30][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:42:44][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%
[09:42:46][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:42:50][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:43:16][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%
[09:43:18][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:43:43][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%
[09:43:45][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:43:59][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%
[09:44:01][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..
[09:44:15][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：13.22%，下次增量：6.5684%
[09:44:33][DEBUG] 场景识别成功：探索
[09:44:33][DEBUG]  [定时任务(1b2ce)] [TanSuo] 当前场景:探索
[09:44:33][DEBUG] 场景识别成功：探索
[09:45:14][DEBUG] 添加任务历史记录：日常任务1，ID：bb3ed8dc-fa84-4423-9250-3a41e421d1db
[09:45:15][DEBUG] 已发送启动命令给模拟器，索引: 1，正在等待模拟器完全启动...
[09:45:16][DEBUG] 模拟器已完全启动成功，索引: 1，main_wnd和render_wnd均已就绪
[09:45:16][DEBUG] 成功解析窗口句柄 - 主窗口: 0x38091E, 渲染窗口: 0x90A30
[09:45:25][DEBUG] 成功获取到模拟器(索引:1)的ADB端口: 16416
[09:45:25][DEBUG] 提供了ADB端口参数 16416，尝试自动连接
[09:45:25][DEBUG] ADB连接命令执行成功，正在检查设备状态...
[09:45:26][DEBUG] 设备 127.0.0.1:16416 状态: device
[09:45:26][DEBUG] 成功连接到ADB设备端口 16416，设备状态正常
[09:45:26][DEBUG] 设备 127.0.0.1:16416 状态: device
[09:45:27][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"

[09:45:33][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19
[09:45:33][DEBUG] Main_Pics 被更新！版本号：1.4.19
[09:45:33][DEBUG]  [定时任务(5e3e1)] 任务线程已经在后台执行，UI控制权转移给用户！
[09:45:33][DEBUG]  [定时任务(5e3e1)] 后台线程开启！
[09:45:38][DEBUG]  [定时任务(5e3e1)] 尝试使用绑定模式 0 绑定窗口
[09:45:39][DEBUG]  [定时任务(5e3e1)] 尝试使用绑定模式 0 绑定窗口
[09:45:40][DEBUG]  [定时任务(5e3e1)] dm_id已捕获：main[579294340] sub[579330316]
[09:45:43][DEBUG]  [定时任务(5e3e1)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭
[09:45:43][DEBUG]  [定时任务(5e3e1)] 小纸人配置 - 基础触发概率: 0.00%
[09:45:45][DEBUG]  [定时任务(5e3e1)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏
[09:46:05][DEBUG]  [定时任务(5e3e1)] 设置的庭院皮肤为：枫色秋庭
[09:46:11][DEBUG] 场景识别成功：庭院
[09:46:11][DEBUG]  [定时任务(5e3e1)] [TingYuan] 当前场景:庭院
[09:46:11][DEBUG] 场景识别成功：庭院
[09:46:20][DEBUG] 场景识别成功：庭院
[09:46:28][DEBUG] YoloClassfly：未知
[09:46:28][DEBUG] AI场景识别成功：未知[YOLO]
[09:46:30][DEBUG] 场景识别成功：庭院
[09:46:35][DEBUG] 场景识别成功：庭院
[09:46:58][DEBUG] 场景识别成功：庭院
[09:47:21][DEBUG] 场景识别成功：庭院
[09:47:27][DEBUG] 场景识别成功：庭院
[09:47:27][DEBUG]  [定时任务(5e3e1)] [TanSuo] 当前场景:庭院
[09:47:27][DEBUG] 场景识别成功：庭院
[09:47:27][DEBUG] 场景识别成功：庭院
[09:47:33][DEBUG] 场景识别成功：探索
[09:47:34][DEBUG] 场景识别成功：探索
[09:47:49][DEBUG] 场景识别成功：探索
[09:47:54][DEBUG]  [定时任务(1b2ce)] 任务:[Sub]被取消..
[09:47:55][DEBUG] 更新任务历史记录状态：21c5e7b4-a21a-4a96-ab7a-e60ec31f26a7，新状态：成功
[09:47:55][DEBUG] 已发送关闭命令给模拟器，索引: 0
[09:48:06][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:48:20][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[09:48:22][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:48:36][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[09:48:38][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:48:42][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:49:01][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[09:49:03][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:49:21][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[09:49:24][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:49:52][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[09:50:02][DEBUG] 场景识别成功：探索
[09:50:19][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:50:33][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[09:50:35][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:50:49][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[09:50:51][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:50:55][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:51:14][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[09:51:16][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:51:34][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%
[09:51:36][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:51:50][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%
[09:51:53][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:51:57][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:52:15][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%
[09:52:17][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:52:32][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%
[09:52:35][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:53:03][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%
[09:53:19][DEBUG] 场景识别成功：探索
[09:54:04][DEBUG] 场景识别成功：探索
[09:54:13][DEBUG] 场景识别成功：探索
[09:54:30][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:54:44][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%
[09:54:46][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:55:34][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:55:38][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:55:52][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[09:55:54][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:56:40][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[09:56:42][DEBUG] 场景识别成功：探索
[09:56:47][DEBUG] 场景识别成功：探索
[09:57:04][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:57:18][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[09:57:20][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:57:24][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:57:38][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[09:57:40][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:57:55][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[09:57:57][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:58:10][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[09:58:13][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:58:27][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[09:58:29][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..
[09:59:09][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[09:59:30][DEBUG] 场景识别成功：探索
[09:59:30][DEBUG]  [定时任务(5e3e1)] [TanSuo] 当前场景:探索
[09:59:30][DEBUG] 场景识别成功：探索
[10:00:41][DEBUG]  [定时任务(5e3e1)] 任务:[Sub]被取消..
[10:00:43][DEBUG] 更新任务历史记录状态：bb3ed8dc-fa84-4423-9250-3a41e421d1db，新状态：成功
[10:00:43][DEBUG] 已发送关闭命令给模拟器，索引: 1
[10:21:12][DEBUG] CheckUserStatus|Running...
[10:21:13][DEBUG] CheckUserStatus|Ended|OK
[10:23:04][DEBUG] 已启动自动刷新，间隔：30秒
[10:23:04][DEBUG] 任务历史记录视图模型已初始化
[10:23:04][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共3条
[10:23:04][DEBUG] 已更新分页，当前第1/1页，每页50条，共3条记录
[10:23:04][DEBUG] 应用过滤器后，显示3/3条记录
[10:23:04][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共3条
[10:23:04][DEBUG] 已刷新统计信息
[10:23:04][DEBUG] 任务历史记录视图模型已初始化完成
[10:23:18][DEBUG] 已停止自动刷新
[10:23:18][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止
[12:32:25][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[12:32:25][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[12:32:25][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[12:32:25][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[12:32:25][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[12:32:25][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[12:32:26][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[12:32:26][DEBUG] WebSocket连接未打开，无法发送对象
[12:32:26][DEBUG] WebSocket连接未打开，无法发送对象
[12:32:26][DEBUG] 初始化.. 删除三天前的日志文件夹..
[12:32:29][DEBUG] WebSocket连接未打开，无法发送对象
[12:32:30][DEBUG] 正在连接到WebSocket服务器
[12:32:31][DEBUG] WebSocket连接成功
[12:32:31][DEBUG] 常规消息，type=welcome
[12:32:31][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1133","clientId":"xsllovemlj-client-1133","username":"xsllovemlj","timestamp":1749097880142}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[12:32:31][DEBUG] 成功连接到WebSocket服务器
[12:32:31][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[12:32:31][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[12:32:31][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[12:32:31][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[12:32:32][DEBUG] InitMuMuConfigs|Running...
[12:32:32][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[12:37:27][DEBUG] CheckUserStatus|Running...
[12:37:28][DEBUG] CheckUserStatus|Ended|OK
[13:32:00][DEBUG] 添加任务历史记录：契灵50，ID：b485f870-8808-4971-baf0-7129cfa03341
[13:32:01][DEBUG] 已发送启动命令给模拟器，索引: 0，正在等待模拟器完全启动...
[13:32:03][DEBUG] 模拟器已完全启动成功，索引: 0，main_wnd和render_wnd均已就绪
[13:32:03][DEBUG] 成功解析窗口句柄 - 主窗口: 0x6E0AC8, 渲染窗口: 0x540ACE
[13:32:12][DEBUG] 成功获取到模拟器(索引:0)的ADB端口: 16384
[13:32:12][DEBUG] 提供了ADB端口参数 16384，尝试自动连接
[13:32:12][DEBUG] ADB连接命令执行成功，正在检查设备状态...
[13:32:13][DEBUG] 设备 127.0.0.1:16384 状态: device
[13:32:13][DEBUG] 成功连接到ADB设备端口 16384，设备状态正常
[13:32:13][DEBUG] 设备 127.0.0.1:16384 状态: device
[13:32:13][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"

[13:32:19][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19
[13:32:19][DEBUG] Main_Pics 被更新！版本号：1.4.19
[13:32:23][DEBUG] 插件注册免注册完成，版本号：7.2450
[13:32:23][DEBUG] 插件收费注册完成，返回值：1
[13:32:23][DEBUG] 插件版本：7.2450
[13:32:23][DEBUG]  [定时任务(dcc38)] 任务线程已经在后台执行，UI控制权转移给用户！
[13:32:23][DEBUG]  [定时任务(dcc38)] 后台线程开启！
[13:32:28][DEBUG]  [定时任务(dcc38)] 尝试使用绑定模式 0 绑定窗口
[13:32:31][DEBUG]  [定时任务(dcc38)] 尝试使用绑定模式 0 绑定窗口
[13:32:32][DEBUG]  [定时任务(dcc38)] dm_id已捕获：main[389665500] sub[389683724]
[13:32:35][DEBUG]  [定时任务(dcc38)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭
[13:32:35][DEBUG]  [定时任务(dcc38)] 小纸人配置 - 基础触发概率: 0.00%
[13:32:36][DEBUG]  [定时任务(dcc38)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏
[13:32:50][DEBUG]  [定时任务(dcc38)] 设置的庭院皮肤为：枫色秋庭
[13:32:55][DEBUG] 场景识别成功：庭院
[13:32:55][DEBUG] 场景识别成功：庭院
[13:32:55][DEBUG]  [定时任务(dcc38)] [TanSuo] 当前场景:庭院
[13:32:55][DEBUG] 场景识别成功：庭院
[13:32:55][DEBUG] 场景识别成功：庭院
[13:33:11][DEBUG] 场景识别成功：探索
[13:33:11][DEBUG] Tesseract识别结果：11/30
[13:33:11][DEBUG]  [定时任务(dcc38)] 本地Ocr识别突破卷结果：11
[13:33:11][DEBUG] 场景识别成功：探索
[13:33:11][DEBUG]  [定时任务(dcc38)] [QiLing] 当前场景:探索
[13:33:11][DEBUG] 场景识别成功：探索
[13:33:11][DEBUG] 场景识别成功：探索
[13:33:11][DEBUG]  [定时任务(dcc38)] [TanSuo] 当前场景:探索
[13:33:11][DEBUG] 场景识别成功：探索
[13:33:15][DEBUG] 场景识别成功：契灵
[13:33:17][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[13:34:01][DEBUG] Ai预测模型加载完成！[Classify]
[13:34:01][DEBUG] YoloClassfly：未知
[13:34:01][DEBUG] AI场景识别成功：未知[YOLO]
[13:34:03][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[13:34:33][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:34:46][DEBUG] YoloClassfly：tansuotask
[13:34:46][DEBUG] AI场景识别成功：探索任务
[13:34:47][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[13:35:29][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[13:36:12][DEBUG] YoloClassfly：未知
[13:36:12][DEBUG] AI场景识别成功：未知[YOLO]
[13:36:13][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[13:36:57][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[13:37:27][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:37:28][DEBUG] CheckUserStatus|Running...
[13:37:28][DEBUG] CheckUserStatus|Ended|OK
[13:37:35][DEBUG] YoloClassfly：未知
[13:37:35][DEBUG] AI场景识别成功：未知[YOLO]
[13:37:37][DEBUG] YoloClassfly：未知
[13:37:37][DEBUG] AI场景识别成功：未知[YOLO]
[13:37:38][DEBUG] YoloClassfly：未知
[13:37:38][DEBUG] AI场景识别成功：未知[YOLO]
[13:37:39][DEBUG] YoloClassfly：未知
[13:37:39][DEBUG] AI场景识别成功：未知[YOLO]
[13:37:40][DEBUG] YoloClassfly：未知
[13:37:40][DEBUG] AI场景识别成功：未知[YOLO]
[13:37:41][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[13:38:24][DEBUG] YoloClassfly：未知
[13:38:24][DEBUG] AI场景识别成功：未知[YOLO]
[13:38:26][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[13:39:08][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%
[13:39:40][DEBUG] YoloClassfly：未知
[13:39:40][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:41][DEBUG] YoloClassfly：未知
[13:39:41][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:42][DEBUG] YoloClassfly：未知
[13:39:42][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:44][DEBUG] YoloClassfly：未知
[13:39:44][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:45][DEBUG] YoloClassfly：未知
[13:39:45][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:46][DEBUG] YoloClassfly：未知
[13:39:46][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:47][DEBUG] YoloClassfly：未知
[13:39:47][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:48][DEBUG] YoloClassfly：未知
[13:39:48][DEBUG] AI场景识别成功：未知[YOLO]
[13:39:50][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%
[13:40:18][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:40:32][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%
[13:41:15][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%
[13:41:57][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%
[13:42:35][DEBUG] YoloClassfly：未知
[13:42:35][DEBUG] AI场景识别成功：未知[YOLO]
[13:42:36][DEBUG] YoloClassfly：未知
[13:42:36][DEBUG] AI场景识别成功：未知[YOLO]
[13:42:37][DEBUG] YoloClassfly：未知
[13:42:37][DEBUG] AI场景识别成功：未知[YOLO]
[13:42:38][DEBUG] YoloClassfly：未知
[13:42:38][DEBUG] AI场景识别成功：未知[YOLO]
[13:42:39][DEBUG] YoloClassfly：未知
[13:42:39][DEBUG] AI场景识别成功：未知[YOLO]
[13:43:34][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:43:47][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[13:44:29][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[13:45:00][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:45:13][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[13:45:51][DEBUG] YoloClassfly：未知
[13:45:51][DEBUG] AI场景识别成功：未知[YOLO]
[13:45:52][DEBUG] YoloClassfly：未知
[13:45:52][DEBUG] AI场景识别成功：未知[YOLO]
[13:45:53][DEBUG] YoloClassfly：未知
[13:45:53][DEBUG] AI场景识别成功：未知[YOLO]
[13:45:54][DEBUG] YoloClassfly：未知
[13:45:54][DEBUG] AI场景识别成功：未知[YOLO]
[13:45:56][DEBUG] YoloClassfly：未知
[13:45:56][DEBUG] AI场景识别成功：未知[YOLO]
[13:45:57][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[13:46:25][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:46:35][DEBUG] YoloClassfly：未知
[13:46:35][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:36][DEBUG] YoloClassfly：未知
[13:46:36][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:37][DEBUG] YoloClassfly：未知
[13:46:37][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:38][DEBUG] YoloClassfly：未知
[13:46:38][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:40][DEBUG] YoloClassfly：未知
[13:46:40][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:41][DEBUG] YoloClassfly：未知
[13:46:41][DEBUG] AI场景识别成功：未知[YOLO]
[13:46:42][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[13:47:11][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:47:20][DEBUG] YoloClassfly：未知
[13:47:20][DEBUG] AI场景识别成功：未知[YOLO]
[13:47:21][DEBUG] YoloClassfly：未知
[13:47:21][DEBUG] AI场景识别成功：未知[YOLO]
[13:47:22][DEBUG] YoloClassfly：tansuo
[13:47:22][DEBUG] AI场景识别成功：探索
[13:47:24][DEBUG] YoloClassfly：未知
[13:47:24][DEBUG] AI场景识别成功：未知[YOLO]
[13:47:25][DEBUG] YoloClassfly：未知
[13:47:25][DEBUG] AI场景识别成功：未知[YOLO]
[13:47:26][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[13:47:55][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:48:09][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[13:48:37][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:48:47][DEBUG] YoloClassfly：未知
[13:48:47][DEBUG] AI场景识别成功：未知[YOLO]
[13:48:48][DEBUG] YoloClassfly：未知
[13:48:48][DEBUG] AI场景识别成功：未知[YOLO]
[13:48:49][DEBUG] YoloClassfly：未知
[13:48:49][DEBUG] AI场景识别成功：未知[YOLO]
[13:48:50][DEBUG] YoloClassfly：未知
[13:48:50][DEBUG] AI场景识别成功：未知[YOLO]
[13:48:51][DEBUG] YoloClassfly：未知
[13:48:51][DEBUG] AI场景识别成功：未知[YOLO]
[13:48:53][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[13:49:21][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:49:31][DEBUG] YoloClassfly：未知
[13:49:31][DEBUG] AI场景识别成功：未知[YOLO]
[13:49:32][DEBUG] YoloClassfly：未知
[13:49:32][DEBUG] AI场景识别成功：未知[YOLO]
[13:49:33][DEBUG] YoloClassfly：tansuo
[13:49:33][DEBUG] AI场景识别成功：探索
[13:49:34][DEBUG] YoloClassfly：未知
[13:49:34][DEBUG] AI场景识别成功：未知[YOLO]
[13:49:35][DEBUG] YoloClassfly：未知
[13:49:35][DEBUG] AI场景识别成功：未知[YOLO]
[13:49:37][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%
[13:50:07][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:50:15][DEBUG] YoloClassfly：未知
[13:50:15][DEBUG] AI场景识别成功：未知[YOLO]
[13:50:17][DEBUG] YoloClassfly：未知
[13:50:17][DEBUG] AI场景识别成功：未知[YOLO]
[13:50:18][DEBUG] YoloClassfly：未知
[13:50:18][DEBUG] AI场景识别成功：未知[YOLO]
[13:50:19][DEBUG] YoloClassfly：未知
[13:50:19][DEBUG] AI场景识别成功：未知[YOLO]
[13:50:20][DEBUG] YoloClassfly：未知
[13:50:20][DEBUG] AI场景识别成功：未知[YOLO]
[13:50:22][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%
[13:51:04][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%
[13:51:46][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%
[13:52:28][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%
[13:52:57][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:53:09][DEBUG] YoloClassfly：未知
[13:53:09][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:11][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%
[13:53:44][DEBUG] YoloClassfly：未知
[13:53:44][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:45][DEBUG] YoloClassfly：未知
[13:53:45][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:46][DEBUG] YoloClassfly：未知
[13:53:46][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:48][DEBUG] YoloClassfly：未知
[13:53:48][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:49][DEBUG] YoloClassfly：未知
[13:53:49][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:50][DEBUG] YoloClassfly：未知
[13:53:50][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:51][DEBUG] YoloClassfly：未知
[13:53:51][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:52][DEBUG] YoloClassfly：未知
[13:53:52][DEBUG] AI场景识别成功：未知[YOLO]
[13:53:54][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%
[13:54:38][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：13.22%，下次增量：6.5684%
[13:55:06][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:55:20][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：19.79%，下次增量：9.8526%
[13:55:50][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:56:00][DEBUG] YoloClassfly：未知
[13:56:00][DEBUG] AI场景识别成功：未知[YOLO]
[13:56:01][DEBUG] YoloClassfly：未知
[13:56:01][DEBUG] AI场景识别成功：未知[YOLO]
[13:56:02][DEBUG] YoloClassfly：tansuo
[13:56:02][DEBUG] AI场景识别成功：探索
[13:56:03][DEBUG] YoloClassfly：未知
[13:56:03][DEBUG] AI场景识别成功：未知[YOLO]
[13:56:04][DEBUG] YoloClassfly：未知
[13:56:04][DEBUG] AI场景识别成功：未知[YOLO]
[13:56:06][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：29.64%，下次增量：14.7789%
[13:56:34][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:56:48][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：44.42%，下次增量：22.1684%
[13:57:17][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[13:57:30][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：66.59%，下次增量：33.2526%
[13:59:35][DEBUG] YoloClassfly：未知
[13:59:35][DEBUG] AI场景识别成功：未知[YOLO]
[13:59:37][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%
[14:00:19][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%
[14:01:00][DEBUG] YoloClassfly：未知
[14:01:00][DEBUG] AI场景识别成功：未知[YOLO]
[14:01:02][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%
[14:01:32][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:01:45][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%
[14:02:16][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:02:28][DEBUG] YoloClassfly：未知
[14:02:28][DEBUG] AI场景识别成功：未知[YOLO]
[14:02:30][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%
[14:02:59][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:03:12][DEBUG] YoloClassfly：未知
[14:03:12][DEBUG] AI场景识别成功：未知[YOLO]
[14:03:13][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%
[14:03:42][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:03:56][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%
[14:04:38][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%
[14:05:06][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:05:16][DEBUG] YoloClassfly：未知
[14:05:16][DEBUG] AI场景识别成功：未知[YOLO]
[14:05:17][DEBUG] YoloClassfly：未知
[14:05:17][DEBUG] AI场景识别成功：未知[YOLO]
[14:05:18][DEBUG] YoloClassfly：未知
[14:05:18][DEBUG] AI场景识别成功：未知[YOLO]
[14:05:19][DEBUG] YoloClassfly：未知
[14:05:19][DEBUG] AI场景识别成功：未知[YOLO]
[14:05:21][DEBUG] YoloClassfly：未知
[14:05:21][DEBUG] AI场景识别成功：未知[YOLO]
[14:05:22][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%
[14:05:52][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:06:06][DEBUG] YoloClassfly：未知
[14:06:06][DEBUG] AI场景识别成功：未知[YOLO]
[14:06:07][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%
[14:06:43][DEBUG] YoloClassfly：未知
[14:06:43][DEBUG] AI场景识别成功：未知[YOLO]
[14:06:44][DEBUG] YoloClassfly：未知
[14:06:44][DEBUG] AI场景识别成功：未知[YOLO]
[14:06:45][DEBUG] YoloClassfly：未知
[14:06:45][DEBUG] AI场景识别成功：未知[YOLO]
[14:06:46][DEBUG] YoloClassfly：tingzhong
[14:06:46][DEBUG] AI场景识别成功：町中
[14:06:47][DEBUG] YoloClassfly：未知
[14:06:47][DEBUG] AI场景识别成功：未知[YOLO]
[14:06:49][DEBUG] YoloClassfly：tansuotask
[14:06:49][DEBUG] AI场景识别成功：探索任务
[14:06:50][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%
[14:07:22][DEBUG] YoloClassfly：未知
[14:07:22][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:24][DEBUG] YoloClassfly：未知
[14:07:24][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:25][DEBUG] YoloClassfly：未知
[14:07:25][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:26][DEBUG] YoloClassfly：未知
[14:07:26][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:27][DEBUG] YoloClassfly：未知
[14:07:27][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:28][DEBUG] YoloClassfly：未知
[14:07:28][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:30][DEBUG] YoloClassfly：未知
[14:07:30][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:31][DEBUG] YoloClassfly：未知
[14:07:31][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:32][DEBUG] YoloClassfly：未知
[14:07:32][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:33][DEBUG] YoloClassfly：未知
[14:07:33][DEBUG] AI场景识别成功：未知[YOLO]
[14:07:34][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%
[14:08:04][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:08:18][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%
[14:08:48][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5
[14:09:01][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%
[14:09:44][DEBUG] YoloClassfly：未知
[14:09:44][DEBUG] AI场景识别成功：未知[YOLO]
[14:09:45][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%
[14:10:34][DEBUG]  [定时任务(dcc38)] 任务:[Sub]被取消..
[14:10:36][DEBUG] 已发送关闭命令给模拟器，索引: 0
[14:10:36][DEBUG] 更新任务历史记录状态：b485f870-8808-4971-baf0-7129cfa03341，新状态：成功
[14:37:28][DEBUG] CheckUserStatus|Running...
[14:37:28][DEBUG] CheckUserStatus|Ended|OK
[15:37:28][DEBUG] CheckUserStatus|Running...
[15:37:28][DEBUG] CheckUserStatus|Ended|OK
[16:37:28][DEBUG] CheckUserStatus|Running...
[16:37:28][DEBUG] CheckUserStatus|Ended|OK
[17:37:28][DEBUG] CheckUserStatus|Running...
[17:37:28][DEBUG] CheckUserStatus|Ended|OK
[18:37:28][DEBUG] CheckUserStatus|Running...
[18:37:28][DEBUG] CheckUserStatus|Ended|OK
[19:01:08][DEBUG] 已启动自动刷新，间隔：30秒
[19:01:08][DEBUG] 任务历史记录视图模型已初始化
[19:01:08][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:01:08][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:01:08][DEBUG] 应用过滤器后，显示4/4条记录
[19:01:08][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[19:01:08][DEBUG] 已刷新统计信息
[19:01:08][DEBUG] 任务历史记录视图模型已初始化完成
[19:01:39][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:01:39][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:01:39][DEBUG] 应用过滤器后，显示4/4条记录
[19:01:39][DEBUG] 自动刷新历史记录完成
[19:01:54][DEBUG] 已更新分页，当前第1/1页，每页50条，共3条记录
[19:01:54][DEBUG] 应用过滤器后，显示3/4条记录
[19:01:58][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录
[19:01:58][DEBUG] 应用过滤器后，显示1/4条记录
[19:02:01][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:02:01][DEBUG] 应用过滤器后，显示4/4条记录
[19:02:10][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:02:10][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:02:10][DEBUG] 应用过滤器后，显示4/4条记录
[19:02:10][DEBUG] 自动刷新历史记录完成
[19:02:40][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:02:40][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:02:40][DEBUG] 应用过滤器后，显示4/4条记录
[19:02:40][DEBUG] 自动刷新历史记录完成
[19:03:10][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:03:10][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:03:10][DEBUG] 应用过滤器后，显示4/4条记录
[19:03:10][DEBUG] 自动刷新历史记录完成
[19:03:40][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:03:40][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:03:40][DEBUG] 应用过滤器后，显示4/4条记录
[19:03:40][DEBUG] 自动刷新历史记录完成
[19:04:10][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[19:04:10][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[19:04:10][DEBUG] 应用过滤器后，显示4/4条记录
[19:04:10][DEBUG] 自动刷新历史记录完成
[19:04:11][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[19:04:11][DEBUG] 已刷新统计信息
[19:04:14][DEBUG] 已停止自动刷新
[19:04:14][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止
[20:22:56][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:22:56][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:22:56][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:22:56][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:22:56][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:22:56][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[20:22:56][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[20:22:56][DEBUG] WebSocket连接未打开，无法发送对象
[20:22:56][DEBUG] WebSocket连接未打开，无法发送对象
[20:22:57][DEBUG] 初始化.. 删除三天前的日志文件夹..
[20:22:59][DEBUG] WebSocket连接未打开，无法发送对象
[20:23:00][DEBUG] 正在连接到WebSocket服务器
[20:23:01][DEBUG] WebSocket连接成功
[20:23:01][DEBUG] 常规消息，type=welcome
[20:23:01][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1142","clientId":"xsllovemlj-client-1142","username":"xsllovemlj","timestamp":1749126110348}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[20:23:01][DEBUG] 成功连接到WebSocket服务器
[20:23:01][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:23:01][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:23:01][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:23:01][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[20:23:01][DEBUG] 常规消息，type=server-response
[20:23:03][DEBUG] InitMuMuConfigs|Running...
[20:23:03][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[20:44:20][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:44:20][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:44:20][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:44:20][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:44:20][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:44:20][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[20:44:20][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[20:44:20][DEBUG] 图库版本参数被默认为: 888
[20:44:21][DEBUG] WebSocket连接未打开，无法发送对象
[20:44:21][DEBUG] WebSocket连接未打开，无法发送对象
[20:44:21][DEBUG] 初始化.. 删除三天前的日志文件夹..
[20:44:23][DEBUG] WebSocket连接未打开，无法发送对象
[20:44:25][DEBUG] 正在连接到WebSocket服务器
[20:44:26][DEBUG] WebSocket连接成功
[20:44:26][DEBUG] 成功连接到WebSocket服务器
[20:44:26][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:44:26][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:44:26][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:44:26][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[20:44:27][DEBUG] 常规消息，type=welcome
[20:44:27][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1143","clientId":"xsllovemlj-client-1143","username":"xsllovemlj","timestamp":1749127395314}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[20:44:28][DEBUG] InitMuMuConfigs|Running...
[20:44:28][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[20:46:35][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:46:35][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:46:35][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:46:35][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:46:35][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[20:46:35][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[20:46:35][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[20:46:35][DEBUG] 图库版本参数被默认为: 888
[20:46:36][DEBUG] WebSocket连接未打开，无法发送对象
[20:46:36][DEBUG] WebSocket连接未打开，无法发送对象
[20:46:36][DEBUG] 初始化.. 删除三天前的日志文件夹..
[20:46:38][DEBUG] WebSocket连接未打开，无法发送对象
[20:46:41][DEBUG] 正在连接到WebSocket服务器
[20:46:41][DEBUG] WebSocket连接成功
[20:46:41][DEBUG] 常规消息，type=welcome
[20:46:41][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1144","clientId":"xsllovemlj-client-1144","username":"xsllovemlj","timestamp":1749127530778}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[20:46:41][DEBUG] 成功连接到WebSocket服务器
[20:46:41][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:46:41][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[20:46:41][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[20:46:41][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[20:46:43][DEBUG] InitMuMuConfigs|Running...
[20:46:43][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[20:48:38][DEBUG] CheckUserStatus|Running...
[20:48:38][DEBUG] CheckUserStatus|Ended|OK
[21:05:14][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:05:14][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:05:14][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:05:14][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:05:14][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:05:14][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[21:05:15][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[21:05:15][DEBUG] 图库版本参数被默认为: 888
[21:05:15][DEBUG] WebSocket连接未打开，无法发送对象
[21:05:15][DEBUG] WebSocket连接未打开，无法发送对象
[21:05:16][DEBUG] 初始化.. 删除三天前的日志文件夹..
[21:05:18][DEBUG] WebSocket连接未打开，无法发送对象
[21:09:21][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:09:21][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:09:21][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:09:21][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:09:21][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:09:21][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[21:09:21][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[21:09:21][DEBUG] 图库版本参数被默认为: 888
[21:09:21][DEBUG] WebSocket连接未打开，无法发送对象
[21:09:21][DEBUG] WebSocket连接未打开，无法发送对象
[21:09:22][DEBUG] 初始化.. 删除三天前的日志文件夹..
[21:09:24][DEBUG] WebSocket连接未打开，无法发送对象
[21:09:26][DEBUG] 正在连接到WebSocket服务器
[21:09:26][DEBUG] WebSocket连接成功
[21:09:26][DEBUG] 成功连接到WebSocket服务器
[21:09:26][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:09:26][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:09:26][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:09:26][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[21:09:27][DEBUG] 常规消息，type=welcome
[21:09:27][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1145","clientId":"xsllovemlj-client-1145","username":"xsllovemlj","timestamp":1749128896155}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[21:09:28][DEBUG] InitMuMuConfigs|Running...
[21:09:28][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[21:09:55][DEBUG] 已启动自动刷新，间隔：30秒
[21:09:55][DEBUG] 任务历史记录视图模型已初始化
[21:09:55][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:09:55][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:09:55][DEBUG] 应用过滤器后，显示4/4条记录
[21:09:55][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[21:09:55][DEBUG] 已刷新统计信息
[21:09:55][DEBUG] 任务历史记录视图模型已初始化完成
[21:10:25][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:10:25][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:10:25][DEBUG] 应用过滤器后，显示4/4条记录
[21:10:25][DEBUG] 自动刷新历史记录完成
[21:10:55][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:10:55][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:10:55][DEBUG] 应用过滤器后，显示4/4条记录
[21:10:55][DEBUG] 自动刷新历史记录完成
[21:11:23][DEBUG] CheckUserStatus|Running...
[21:11:24][DEBUG] CheckUserStatus|Ended|OK
[21:11:25][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:11:25][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:11:25][DEBUG] 应用过滤器后，显示4/4条记录
[21:11:25][DEBUG] 自动刷新历史记录完成
[21:12:04][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:12:04][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:12:04][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:12:04][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:12:04][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:12:04][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[21:12:04][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[21:12:04][DEBUG] 图库版本参数被默认为: 888
[21:12:05][DEBUG] WebSocket连接未打开，无法发送对象
[21:12:05][DEBUG] WebSocket连接未打开，无法发送对象
[21:12:05][DEBUG] 初始化.. 删除三天前的日志文件夹..
[21:12:07][DEBUG] WebSocket连接未打开，无法发送对象
[21:12:09][DEBUG] 正在连接到WebSocket服务器
[21:12:10][DEBUG] WebSocket连接成功
[21:12:10][DEBUG] 常规消息，type=welcome
[21:12:10][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1146","clientId":"xsllovemlj-client-1146","username":"xsllovemlj","timestamp":1749129059522}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[21:12:10][DEBUG] 成功连接到WebSocket服务器
[21:12:10][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:12:10][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:12:10][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:12:10][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[21:12:11][DEBUG] InitMuMuConfigs|Running...
[21:12:11][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[21:12:13][DEBUG] 已启动自动刷新，间隔：30秒
[21:12:13][DEBUG] 任务历史记录视图模型已初始化
[21:12:13][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:12:13][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:12:13][DEBUG] 应用过滤器后，显示4/4条记录
[21:12:13][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[21:12:13][DEBUG] 已刷新统计信息
[21:12:13][DEBUG] 任务历史记录视图模型已初始化完成
[21:12:43][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:12:43][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:12:43][DEBUG] 应用过滤器后，显示4/4条记录
[21:12:43][DEBUG] 自动刷新历史记录完成
[21:13:13][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:13:13][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:13:13][DEBUG] 应用过滤器后，显示4/4条记录
[21:13:13][DEBUG] 自动刷新历史记录完成
[21:13:35][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:13:35][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:13:35][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:13:35][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:13:35][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器
[21:13:35][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册
[21:13:35][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器
[21:13:35][DEBUG] 图库版本参数被默认为: 888
[21:13:36][DEBUG] WebSocket连接未打开，无法发送对象
[21:13:36][DEBUG] WebSocket连接未打开，无法发送对象
[21:13:36][DEBUG] 初始化.. 删除三天前的日志文件夹..
[21:13:38][DEBUG] WebSocket连接未打开，无法发送对象
[21:13:40][DEBUG] 正在连接到WebSocket服务器
[21:13:41][DEBUG] WebSocket连接成功
[21:13:41][DEBUG] 常规消息，type=welcome
[21:13:41][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1149","clientId":"xsllovemlj-client-1149","username":"xsllovemlj","timestamp":1749129150609}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND
[21:13:41][DEBUG] 成功连接到WebSocket服务器
[21:13:41][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:13:41][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器
[21:13:41][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器
[21:13:41][DEBUG] WebSocket连接成功后，已重新注册消息处理器
[21:13:43][DEBUG] InitMuMuConfigs|Running...
[21:13:43][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效
[21:13:44][DEBUG] 已启动自动刷新，间隔：30秒
[21:13:44][DEBUG] 任务历史记录视图模型已初始化
[21:13:44][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:13:44][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:13:44][DEBUG] 应用过滤器后，显示4/4条记录
[21:13:44][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[21:13:44][DEBUG] 已刷新统计信息
[21:13:44][DEBUG] 任务历史记录视图模型已初始化完成
[21:14:14][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:14:14][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:14:14][DEBUG] 应用过滤器后，显示4/4条记录
[21:14:14][DEBUG] 自动刷新历史记录完成
[21:14:44][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:14:44][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:14:44][DEBUG] 应用过滤器后，显示4/4条记录
[21:14:44][DEBUG] 自动刷新历史记录完成
[21:15:14][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:15:14][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:15:14][DEBUG] 应用过滤器后，显示4/4条记录
[21:15:14][DEBUG] 自动刷新历史记录完成
[21:15:38][DEBUG] CheckUserStatus|Running...
[21:15:38][DEBUG] CheckUserStatus|Ended|OK
[21:15:44][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:15:44][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:15:44][DEBUG] 应用过滤器后，显示4/4条记录
[21:15:44][DEBUG] 自动刷新历史记录完成
[21:16:14][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:16:14][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:16:14][DEBUG] 应用过滤器后，显示4/4条记录
[21:16:14][DEBUG] 自动刷新历史记录完成
[21:16:44][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:16:44][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:16:44][DEBUG] 应用过滤器后，显示4/4条记录
[21:16:44][DEBUG] 自动刷新历史记录完成
[21:17:14][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:17:14][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:17:14][DEBUG] 应用过滤器后，显示4/4条记录
[21:17:14][DEBUG] 自动刷新历史记录完成
[21:17:44][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:17:44][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:17:44][DEBUG] 应用过滤器后，显示4/4条记录
[21:17:44][DEBUG] 自动刷新历史记录完成
[21:18:00][DEBUG] 已停止自动刷新
[21:18:00][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止
[21:18:02][DEBUG] 已启动自动刷新，间隔：30秒
[21:18:02][DEBUG] 任务历史记录视图模型已初始化
[21:18:02][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条
[21:18:02][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录
[21:18:02][DEBUG] 应用过滤器后，显示4/4条记录
[21:18:02][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条
[21:18:02][DEBUG] 已刷新统计信息
[21:18:02][DEBUG] 任务历史记录视图模型已初始化完成
