﻿#pragma checksum "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E2DD1EECCD784D9D3BB0FFE6AA2B4B1D53F5B985"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.UserControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.UserControls {
    
    
    /// <summary>
    /// SingleGamePreviewItem
    /// </summary>
    public partial class SingleGamePreviewItem : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 38 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GameNameText;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SyncButton;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas PreviewCanvas;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoPreviewText;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewErrorText;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastLogText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/usercontrols/singlegamepreviewitem.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.GameNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SyncButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 49 "..\..\..\..\..\Views\UserControls\SingleGamePreviewItem.xaml"
            this.SyncButton.Click += new System.Windows.RoutedEventHandler(this.SyncButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.PreviewCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 5:
            this.NoPreviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PreviewErrorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LastLogText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

