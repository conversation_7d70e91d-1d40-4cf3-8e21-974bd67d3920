using DanDing1.ViewModels.Windows;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// TaskHistoryWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TaskHistoryWindow : Window
    {
        private TaskHistoryViewModel _viewModel;
        private DispatcherTimer _expandTimer;

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskHistoryWindow()
        {
            // 在设计时可能看不到该方法，但它将在编译时由 XAML 编译器生成
            InitializeComponent();
            _viewModel = DataContext as TaskHistoryViewModel;

            // 注册窗口关闭事件
            this.Closing += Window_Closing;
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        /// <param name="emulatorNames">可用的模拟器名称列表</param>
        public void Initialize(List<string> emulatorNames)
        {
            _viewModel?.Initialize(emulatorNames);
        }

        /// <summary>
        /// 历史记录选择变更事件处理
        /// </summary>
        private void HistoryRecords_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // 选择变更事件处理
                // 注意：绑定已经自动将选中项设置到ViewModel的SelectedRecord属性

                // 停止之前的定时器
                _expandTimer?.Stop();

                // 自动展开详细信息面板
                if (e.AddedItems != null && e.AddedItems.Count > 0)
                {
                    // 有新选中的项目时，延迟展开详细信息面板以提供更好的用户体验
                    ExpandDetailsPanel();

                    // 记录日志
                    var selectedItem = e.AddedItems[0];
                    var recordId = GetRecordId(selectedItem);
                    XLogger.Debug($"选中历史记录，准备展开详细信息面板，记录ID: {recordId}");
                }
                else if (e.RemovedItems != null && e.RemovedItems.Count > 0 && (e.AddedItems == null || e.AddedItems.Count == 0))
                {
                    // 取消选择时的处理
                    // 这里选择保持展开状态，用户体验更好
                    // 如果需要自动收起，可以调用 CollapseDetailsPanel() 方法
                    XLogger.Debug("取消选择历史记录，保持详细信息面板展开状态");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"处理历史记录选择变更事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 展开详细信息面板
        /// </summary>
        private void ExpandDetailsPanel()
        {
            try
            {
                if (TaskDetailsExpander == null) return;

                // 使用定时器延迟展开，确保数据绑定完成
                _expandTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(100) // 100ms延迟
                };

                _expandTimer.Tick += (s, e) =>
                {
                    _expandTimer.Stop();

                    // 展开面板
                    TaskDetailsExpander.IsExpanded = true;

                    // 延迟滚动到视图，确保展开动画完成
                    var scrollTimer = new DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(300) // 300ms后滚动
                    };

                    scrollTimer.Tick += (ss, ee) =>
                    {
                        scrollTimer.Stop();

                        // 平滑滚动到详细信息面板
                        TaskDetailsExpander.BringIntoView();

                        XLogger.Debug("详细信息面板已展开并滚动到视图");
                    };

                    scrollTimer.Start();
                };

                _expandTimer.Start();
            }
            catch (Exception ex)
            {
                XLogger.Error($"展开详细信息面板时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 收起详细信息面板
        /// </summary>
        private void CollapseDetailsPanel()
        {
            try
            {
                if (TaskDetailsExpander != null)
                {
                    TaskDetailsExpander.IsExpanded = false;
                    XLogger.Debug("详细信息面板已收起");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"收起详细信息面板时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取记录ID（用于日志记录）
        /// </summary>
        /// <param name="item">选中的项目</param>
        /// <returns>记录ID字符串</returns>
        private string GetRecordId(object item)
        {
            try
            {
                if (item == null) return "null";

                // 尝试通过反射获取Id属性
                var idProperty = item.GetType().GetProperty("Id");
                if (idProperty != null)
                {
                    var id = idProperty.GetValue(item);
                    return id?.ToString() ?? "null";
                }

                // 如果没有Id属性，尝试获取其他标识属性
                var taskIdProperty = item.GetType().GetProperty("TaskId");
                if (taskIdProperty != null)
                {
                    var taskId = taskIdProperty.GetValue(item);
                    return $"TaskId:{taskId}";
                }

                return item.GetType().Name;
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 停止展开定时器
                _expandTimer?.Stop();
                _expandTimer = null;

                // 通过将IsAutoRefreshEnabled设为false来停止自动刷新定时器
                if (_viewModel != null)
                {
                    _viewModel.IsAutoRefreshEnabled = false;
                }

                XLogger.Debug("任务历史记录窗口已关闭，所有定时器已停止");
            }
            catch (System.Exception ex)
            {
                XLogger.Error($"关闭任务历史记录窗口时出错: {ex.Message}");
            }
        }
    }
}