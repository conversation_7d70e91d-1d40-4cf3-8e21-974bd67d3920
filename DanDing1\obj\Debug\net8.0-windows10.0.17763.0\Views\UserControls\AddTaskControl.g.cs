﻿#pragma checksum "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BC31D8AC0CB637D0778CFB610A1250AFC1AC07CC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Helpers;
using DanDing1.Resources;
using DanDing1.ViewModels.Pages;
using DanDing1.Views.UserControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.UserControls {
    
    
    /// <summary>
    /// AddTaskControl
    /// </summary>
    public partial class AddTaskControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 46 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NomalMode1;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NomalMode2;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox GameScene_ComboBox;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.ToggleSwitch XShang_Status;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResourcesTip;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button OpenResources;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button ReloadResources;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Yhun_Level;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Yhun_ZuDui;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Yhun_ZuDui_Location;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Yhun_ZuDui_SelectButton;
        
        #line default
        #line hidden
        
        
        #line 387 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_YuHun;
        
        #line default
        #line hidden
        
        
        #line 480 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Tpo;
        
        #line default
        #line hidden
        
        
        #line 550 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TSuo_ZuDui;
        
        #line default
        #line hidden
        
        
        #line 555 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TSuo_ZuDui_Location;
        
        #line default
        #line hidden
        
        
        #line 561 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TSuo_ZuDui_SelectButton;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_TSuo;
        
        #line default
        #line hidden
        
        
        #line 646 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Yling;
        
        #line default
        #line hidden
        
        
        #line 717 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Dji;
        
        #line default
        #line hidden
        
        
        #line 783 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YingJie_Yling;
        
        #line default
        #line hidden
        
        
        #line 920 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Liudao;
        
        #line default
        #line hidden
        
        
        #line 991 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Qiling;
        
        #line default
        #line hidden
        
        
        #line 1080 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_RiChang;
        
        #line default
        #line hidden
        
        
        #line 1252 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_HDong;
        
        #line default
        #line hidden
        
        
        #line 1429 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YuShe_Qta;
        
        #line default
        #line hidden
        
        
        #line 1458 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton Open_Buff;
        
        #line default
        #line hidden
        
        
        #line 1466 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Buff_jb;
        
        #line default
        #line hidden
        
        
        #line 1468 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Buff_jy;
        
        #line default
        #line hidden
        
        
        #line 1475 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Buff_jx;
        
        #line default
        #line hidden
        
        
        #line 1478 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Buff_yh;
        
        #line default
        #line hidden
        
        
        #line 1483 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton Close_Buff;
        
        #line default
        #line hidden
        
        
        #line 1491 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OnlyNextTip;
        
        #line default
        #line hidden
        
        
        #line 1498 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OnlyNext;
        
        #line default
        #line hidden
        
        
        #line 1569 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBlock GameName;
        
        #line default
        #line hidden
        
        
        #line 1576 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.ListView GameTaskLists;
        
        #line default
        #line hidden
        
        
        #line 1624 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button TasksManager;
        
        #line default
        #line hidden
        
        
        #line 1639 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.InfoBar InfoBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/usercontrols/addtaskcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NomalMode1 = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.NomalMode2 = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.GameScene_ComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.XShang_Status = ((Wpf.Ui.Controls.ToggleSwitch)(target));
            return;
            case 6:
            this.ResourcesTip = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.OpenResources = ((Wpf.Ui.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.OpenResources.Click += new System.Windows.RoutedEventHandler(this.OpenResources_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ReloadResources = ((Wpf.Ui.Controls.Button)(target));
            
            #line 201 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.ReloadResources.Click += new System.Windows.RoutedEventHandler(this.ReloadResources_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.Yhun_Level = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.Yhun_ZuDui = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.Yhun_ZuDui_Location = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.Yhun_ZuDui_SelectButton = ((System.Windows.Controls.Button)(target));
            return;
            case 13:
            this.YuShe_YuHun = ((System.Windows.Controls.ComboBox)(target));
            
            #line 389 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_YuHun.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 393 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_YuHun.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 14:
            this.YuShe_Tpo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 482 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Tpo.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 486 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Tpo.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 15:
            this.TSuo_ZuDui = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.TSuo_ZuDui_Location = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.TSuo_ZuDui_SelectButton = ((System.Windows.Controls.Button)(target));
            return;
            case 18:
            this.YuShe_TSuo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 581 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_TSuo.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 585 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_TSuo.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 19:
            this.YuShe_Yling = ((System.Windows.Controls.ComboBox)(target));
            
            #line 648 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Yling.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 652 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Yling.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 20:
            this.YuShe_Dji = ((System.Windows.Controls.ComboBox)(target));
            
            #line 719 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Dji.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 723 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Dji.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 21:
            this.YingJie_Yling = ((System.Windows.Controls.ComboBox)(target));
            
            #line 785 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YingJie_Yling.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 789 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YingJie_Yling.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 22:
            this.YuShe_Liudao = ((System.Windows.Controls.ComboBox)(target));
            
            #line 922 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Liudao.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 926 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Liudao.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 23:
            this.YuShe_Qiling = ((System.Windows.Controls.ComboBox)(target));
            
            #line 993 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Qiling.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 997 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Qiling.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 24:
            this.YuShe_RiChang = ((System.Windows.Controls.ComboBox)(target));
            
            #line 1082 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_RiChang.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 1086 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_RiChang.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 25:
            this.YuShe_HDong = ((System.Windows.Controls.ComboBox)(target));
            
            #line 1254 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_HDong.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 1258 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_HDong.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 26:
            this.YuShe_Qta = ((System.Windows.Controls.ComboBox)(target));
            
            #line 1431 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Qta.DropDownOpened += new System.EventHandler(this.YuShe_YuHun_DropDownOpened);
            
            #line default
            #line hidden
            
            #line 1435 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.YuShe_Qta.Loaded += new System.Windows.RoutedEventHandler(this.YuShe_YuHun_Loaded);
            
            #line default
            #line hidden
            return;
            case 27:
            this.Open_Buff = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 28:
            this.Buff_jb = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.Buff_jy = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.Buff_jx = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 31:
            this.Buff_yh = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.Close_Buff = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 33:
            this.OnlyNextTip = ((System.Windows.Controls.TextBlock)(target));
            
            #line 1492 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.OnlyNextTip.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.OnlyNextTip_MouseDown);
            
            #line default
            #line hidden
            return;
            case 34:
            this.OnlyNext = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.GameName = ((Wpf.Ui.Controls.TextBlock)(target));
            return;
            case 36:
            this.GameTaskLists = ((Wpf.Ui.Controls.ListView)(target));
            return;
            case 37:
            this.TasksManager = ((Wpf.Ui.Controls.Button)(target));
            
            #line 1628 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            this.TasksManager.MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TasksManager_MouseRightButtonUp);
            
            #line default
            #line hidden
            return;
            case 38:
            this.InfoBar = ((Wpf.Ui.Controls.InfoBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 3:
            
            #line 70 "..\..\..\..\..\Views\UserControls\AddTaskControl.xaml"
            ((System.Windows.Controls.ScrollViewer)(target)).PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

