﻿#pragma checksum "..\..\..\..\..\Views\Windows\LogWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4155A892F555579C2E670BB7FAF48F6ECC1E54A5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// LogWindow
    /// </summary>
    public partial class LogWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 86 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBlock Title;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleEdit;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button close;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Zd;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button AdhesionButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button TasksManager;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button GameControl_Button;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.SymbolIcon GameControl_Icon;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl itemsControl;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/logwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 20 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            ((DanDing1.Views.Windows.LogWindow)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Window_MouseDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.Title = ((Wpf.Ui.Controls.TextBlock)(target));
            
            #line 90 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.Title.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Title_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TitleEdit = ((System.Windows.Controls.TextBox)(target));
            
            #line 98 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.TitleEdit.KeyDown += new System.Windows.Input.KeyEventHandler(this.TitleEdit_KeyDown);
            
            #line default
            #line hidden
            
            #line 99 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.TitleEdit.LostFocus += new System.Windows.RoutedEventHandler(this.TitleEdit_LostFocus);
            
            #line default
            #line hidden
            return;
            case 4:
            this.close = ((Wpf.Ui.Controls.Button)(target));
            
            #line 110 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.close.Click += new System.Windows.RoutedEventHandler(this.close_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Zd = ((System.Windows.Controls.CheckBox)(target));
            
            #line 122 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.Zd.Checked += new System.Windows.RoutedEventHandler(this.Zd_Checked);
            
            #line default
            #line hidden
            
            #line 122 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.Zd.Unchecked += new System.Windows.RoutedEventHandler(this.Zd_Checked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AdhesionButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 127 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.AdhesionButton.Click += new System.Windows.RoutedEventHandler(this.AdhesionButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TasksManager = ((Wpf.Ui.Controls.Button)(target));
            
            #line 129 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.TasksManager.Click += new System.Windows.RoutedEventHandler(this.TasksManager_Click);
            
            #line default
            #line hidden
            
            #line 129 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.TasksManager.MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TasksManager_MouseRightButtonUp);
            
            #line default
            #line hidden
            return;
            case 8:
            this.GameControl_Button = ((Wpf.Ui.Controls.Button)(target));
            
            #line 146 "..\..\..\..\..\Views\Windows\LogWindow.xaml"
            this.GameControl_Button.Click += new System.Windows.RoutedEventHandler(this.GameControl_Button_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.GameControl_Icon = ((Wpf.Ui.Controls.SymbolIcon)(target));
            return;
            case 10:
            this.itemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

