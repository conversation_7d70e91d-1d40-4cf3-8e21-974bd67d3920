[2025-06-05 00:21:12.5586][DEBUG] CheckUserStatus|Running...  
[2025-06-05 00:21:12.7371][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 01:21:12.5776][DEBUG] CheckUserStatus|Running...  
[2025-06-05 01:21:12.7592][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 02:21:12.5945][DEBUG] CheckUserStatus|Running...  
[2025-06-05 02:21:12.8264][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 03:21:12.6132][DEBUG] CheckUserStatus|Running...  
[2025-06-05 03:21:12.8384][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 04:21:12.6311][DEBUG] CheckUserStatus|Running...  
[2025-06-05 04:21:12.8370][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 05:21:12.6582][DEBUG] CheckUserStatus|Running...  
[2025-06-05 05:21:12.9326][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 06:21:12.7162][DEBUG] CheckUserStatus|Running...  
[2025-06-05 06:21:12.9583][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 07:21:12.7431][DEBUG] CheckUserStatus|Running...  
[2025-06-05 07:21:12.9624][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 08:21:12.7642][DEBUG] CheckUserStatus|Running...  
[2025-06-05 08:21:13.0273][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 08:24:52.9205][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 08:24:52.9205][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 08:24:52.9205][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 08:24:52.9325][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共1条  
[2025-06-05 08:24:52.9325][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录  
[2025-06-05 08:24:52.9325][DEBUG] 应用过滤器后，显示1/1条记录  
[2025-06-05 08:24:52.9325][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共1条  
[2025-06-05 08:24:52.9325][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共1条  
[2025-06-05 08:24:52.9325][DEBUG] 已刷新统计信息  
[2025-06-05 08:24:52.9325][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 08:25:04.0212][DEBUG] 已停止自动刷新  
[2025-06-05 08:25:04.0212][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止  
[2025-06-05 08:25:12.2978][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 08:25:12.2978][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 08:25:12.2978][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 08:25:12.3073][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共1条  
[2025-06-05 08:25:12.3073][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录  
[2025-06-05 08:25:12.3073][DEBUG] 应用过滤器后，显示1/1条记录  
[2025-06-05 08:25:12.3073][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共1条  
[2025-06-05 08:25:12.3073][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共1条  
[2025-06-05 08:25:12.3073][DEBUG] 已刷新统计信息  
[2025-06-05 08:25:12.3073][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 08:25:16.9902][DEBUG] 已停止自动刷新  
[2025-06-05 08:25:16.9902][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止  
[2025-06-05 09:21:12.7833][DEBUG] CheckUserStatus|Running...  
[2025-06-05 09:21:13.0118][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 09:33:15.6091][DEBUG] 添加任务历史记录：日常任务1，ID：21c5e7b4-a21a-4a96-ab7a-e60ec31f26a7  
[2025-06-05 09:33:16.6485][DEBUG] 已发送启动命令给模拟器，索引: 0，正在等待模拟器完全启动...  
[2025-06-05 09:33:17.6858][DEBUG] 等待main_wnd就绪，索引: 0  
[2025-06-05 09:33:17.6858][DEBUG] 等待render_wnd就绪，索引: 0  
[2025-06-05 09:33:20.7229][DEBUG] 模拟器已完全启动成功，索引: 0，main_wnd和render_wnd均已就绪  
[2025-06-05 09:33:20.7229][DEBUG] 成功解析窗口句柄 - 主窗口: 0xC50934, 渲染窗口: 0x1208F4  
[2025-06-05 09:33:22.7230][ INFO] 成功获取模拟器和游戏句柄: MumuHandle=12912948, GameHandle=1181940  
[2025-06-05 09:33:29.7755][DEBUG] 成功获取到模拟器(索引:0)的ADB端口: 16384  
[2025-06-05 09:33:29.7755][DEBUG] 提供了ADB端口参数 16384，尝试自动连接  
[2025-06-05 09:33:29.8267][DEBUG] ADB连接命令执行成功，正在检查设备状态...  
[2025-06-05 09:33:30.8809][DEBUG] 设备 127.0.0.1:16384 状态: device  
[2025-06-05 09:33:30.8809][DEBUG] 成功连接到ADB设备端口 16384，设备状态正常  
[2025-06-05 09:33:30.9247][DEBUG] 设备 127.0.0.1:16384 状态: device  
[2025-06-05 09:33:31.2191][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"
  
[2025-06-05 09:33:36.2441][ INFO] 已启动阴阳师应用  
[2025-06-05 09:33:37.2916][ INFO] 图库初始化中. 图库源：本地，图库版本：1.4.19  
[2025-06-05 09:33:37.2939][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19  
[2025-06-05 09:33:37.3098][DEBUG] Main_Pics 被更新！版本号：1.4.19  
[2025-06-05 09:33:37.3098][ INFO] 图库初始化完成，图库版本：1.4.19 载入状态：False 图库数量：673 字库数量：25  
[2025-06-05 09:33:37.3098][ INFO] 初始化大漠构建器成功  
[2025-06-05 09:33:37.6599][DEBUG]  [定时任务(1b2ce)] 任务线程已经在后台执行，UI控制权转移给用户！  
[2025-06-05 09:33:37.6599][ INFO] 任务'日常任务1'启动成功  
[2025-06-05 09:33:37.6599][ INFO] 开始监控任务 [大号_1b2ce] 执行超时，最长执行时间：3600秒  
[2025-06-05 09:33:37.6599][DEBUG]  [定时任务(1b2ce)] 后台线程开启！  
[2025-06-05 09:33:37.6599][ INFO]  [定时任务(1b2ce)] 欢迎使用蛋定助手，遇到异常、停止请蛋定，仔细查看帮助链接，请确保您的模拟器设置符合要求！
若有意料之外的错误，您可以加群(621016172)咨询管理员，谢谢！
5S后将继续执行，再说一遍：请确保您模拟器的设置符合我们的要求(分辨率、渲染引擎···)！  
[2025-06-05 09:33:42.6634][ INFO] 点击位置更新完成，更新模式: 胜利/失败[关闭], 达摩[关闭]  
[2025-06-05 09:33:42.6634][ INFO] 定时任务(1b2ce) 当前执行: 绑定游戏  
[2025-06-05 09:33:42.6634][ INFO]  [定时任务(1b2ce)] 句柄[1181940],开始执行脚本流程！  
[2025-06-05 09:33:42.6694][ INFO]  [定时任务(1b2ce)] 主要对象开始绑定[主对象]  
[2025-06-05 09:33:42.6694][DEBUG]  [定时任务(1b2ce)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 09:33:44.4166][ INFO]  [定时任务(1b2ce)] 辅助对象开始绑定[检测对象]  
[2025-06-05 09:33:44.4341][DEBUG]  [定时任务(1b2ce)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 09:33:45.4974][DEBUG]  [定时任务(1b2ce)] dm_id已捕获：main[579116364] sub[579144764]  
[2025-06-05 09:33:46.6477][ INFO]  [定时任务(1b2ce)] 当前系统CPU：Intel Intel(R) Core(TM) i5-14600KF，显卡信息：NVIDIA GeForce RTX 4060 Ti，DPI是否为100%：1，系统OS版本：10.0.26100.3912 (WinBuild.160101.0800)  
[2025-06-05 09:33:48.6522][DEBUG]  [定时任务(1b2ce)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭  
[2025-06-05 09:33:48.6843][DEBUG]  [定时任务(1b2ce)] 小纸人配置 - 基础触发概率: 0.00%  
[2025-06-05 09:33:49.6493][ INFO]  [定时任务(1b2ce)] 等待游戏到庭院后再启动主任务！  
[2025-06-05 09:33:49.7565][DEBUG]  [定时任务(1b2ce)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏  
[2025-06-05 09:33:50.8613][ INFO]  [定时任务(1b2ce)] 开始将游戏场景操作至庭院！  
[2025-06-05 09:33:53.1742][ INFO]  [定时任务(1b2ce)] 当前默认的服务器为：夜摩天殿(非准确)，点击进入游戏..  
[2025-06-05 09:33:54.9823][ INFO]  [定时任务(1b2ce)] 请确保您的卷轴皮肤为默认皮肤！..  
[2025-06-05 09:33:56.5894][ INFO]  [定时任务(1b2ce)] 关闭牛皮癣广告弹窗..  
[2025-06-05 09:34:00.2607][ INFO]  [定时任务(1b2ce)] 关闭牛皮癣广告弹窗..  
[2025-06-05 09:34:03.4883][ INFO]  [定时任务(1b2ce)] 重启恢复游戏场景完成，当前界面为庭院，为您继续完成任务！  
[2025-06-05 09:34:03.6599][ INFO]  [定时任务(1b2ce)] 开始分析依次执行任务清单！列表整体循环次数：1  
[2025-06-05 09:34:03.6599][DEBUG]  [定时任务(1b2ce)] 设置的庭院皮肤为：枫色秋庭  
[2025-06-05 09:34:03.6599][ INFO] 定时任务(1b2ce) 当前执行: 初始定时任务  
[2025-06-05 09:34:03.6843][ INFO]  [定时任务(1b2ce)] 开始执行第1次任务循环！  
[2025-06-05 09:34:03.6843][ INFO] 定时任务(1b2ce) 当前执行: 日常任务  
[2025-06-05 09:34:03.6843][ INFO]  [定时任务(1b2ce)] 开始执行任务：日常任务 1次  
[2025-06-05 09:34:04.7299][ INFO]  [定时任务(1b2ce)] 任务：[] 重启游戏等事件监听已开启...  
[2025-06-05 09:34:05.7702][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-每日签到！  
[2025-06-05 09:34:06.8325][ INFO]  [定时任务(1b2ce)] 任务：[签到] 重启游戏等事件监听已开启...  
[2025-06-05 09:34:07.9407][ INFO]  [定时任务(1b2ce)] 开始执行任务：日常任务  
[2025-06-05 09:34:10.1881][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:34:10.1881][DEBUG]  [定时任务(1b2ce)] [TingYuan] 当前场景:庭院  
[2025-06-05 09:34:10.2805][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:34:10.2805][ INFO]  [定时任务(1b2ce)] 首次启动，庭院尚未初始化，进入图鉴初始化人物位置..  
[2025-06-05 09:34:18.8556][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:34:18.8556][ INFO]  [定时任务(1b2ce)] 已确认在庭院场景  
[2025-06-05 09:34:22.3862][ INFO]  [定时任务(1b2ce)] 点击签到完成..开奖..  
[2025-06-05 09:34:26.9075][ INFO]  [定时任务(1b2ce)] 截图->退出..  
[2025-06-05 09:34:27.0355][DEBUG] Ai预测模型加载完成！[Classify]  
[2025-06-05 09:34:27.0355][DEBUG] YoloClassfly：未知  
[2025-06-05 09:34:27.0355][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 09:34:29.4691][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:34:29.4691][ INFO]  [定时任务(1b2ce)] 每日签到任务完成  
[2025-06-05 09:34:31.5019][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-友情点！  
[2025-06-05 09:34:32.5720][ INFO]  [定时任务(1b2ce)] 任务：[友情点] 重启游戏等事件监听已开启...  
[2025-06-05 09:34:33.6708][ INFO]  [定时任务(1b2ce)] 开始执行任务：日常任务  
[2025-06-05 09:34:33.7583][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:34:37.6630][ INFO] 任务 [大号_1b2ce] 已运行 60 秒，超时阈值：3600 秒  
[2025-06-05 09:34:39.3371][ERROR] OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..  
[2025-06-05 09:34:40.1511][ INFO]  [定时任务(1b2ce)] 未在好友界面，尝试打开好友界面  
[2025-06-05 09:34:44.6946][ INFO]  [定时任务(1b2ce)] 切换到友情点标签  
[2025-06-05 09:34:47.1781][ INFO]  [定时任务(1b2ce)] 检测到可领取的友情点，开始领取  
[2025-06-05 09:34:49.4679][ INFO]  [定时任务(1b2ce)] 友情点领取完成  
[2025-06-05 09:34:53.7566][ INFO]  [定时任务(1b2ce)] 已关闭好友界面，结束任务..  
[2025-06-05 09:34:55.7933][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-每日一抽！  
[2025-06-05 09:34:56.8331][ INFO]  [定时任务(1b2ce)] 任务：[抽卡] 重启游戏等事件监听已开启...  
[2025-06-05 09:34:57.8997][ WARN]  [定时任务(1b2ce)] 当前进行的是每日免费抽卡任务，支持的召唤屋皮肤有：  
[2025-06-05 09:34:57.9335][ WARN]  [定时任务(1b2ce)] 故梦雅苑、四时雅苑、万事皆灵(3s后继续)  
[2025-06-05 09:35:01.4705][ INFO]  [定时任务(1b2ce)] 前往抽卡任务场景..  
[2025-06-05 09:35:01.5707][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:35:01.5707][DEBUG]  [定时任务(1b2ce)] [TingYuan] 当前场景:庭院  
[2025-06-05 09:35:01.6559][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:35:02.6819][ INFO]  [定时任务(1b2ce)] 滑动一下屏幕.  
[2025-06-05 09:35:09.6567][DEBUG] Tesseract识别结果：35  
[2025-06-05 09:35:09.6567][ INFO]  [定时任务(1b2ce)] 当前蓝票数量：35 张  
[2025-06-05 09:35:10.1378][ INFO]  [定时任务(1b2ce)] 可以抽卡  
[2025-06-05 09:35:10.1378][ INFO]  [定时任务(1b2ce)] 点击蓝票区域..  
[2025-06-05 09:35:11.5625][ INFO]  [定时任务(1b2ce)] 开始画符抽卡..  
[2025-06-05 09:35:18.3165][ INFO]  [定时任务(1b2ce)] 等待结果..  
[2025-06-05 09:35:21.0107][ERROR] OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..  
[2025-06-05 09:35:21.7823][ INFO]  [定时任务(1b2ce)] 等待出现再次召唤..  
[2025-06-05 09:35:24.7249][ INFO]  [定时任务(1b2ce)] 等待出现再次召唤..  
[2025-06-05 09:35:27.6448][ INFO]  [定时任务(1b2ce)] 再次召唤出现，截图，并点击确定..  
[2025-06-05 09:35:29.9580][ INFO]  [定时任务(1b2ce)] 抽卡任务结束..退出到庭院..  
[2025-06-05 09:35:34.5892][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-每日免费礼包！  
[2025-06-05 09:35:35.6403][ INFO]  [定时任务(1b2ce)] 任务：[礼包] 重启游戏等事件监听已开启...  
[2025-06-05 09:35:36.6941][ INFO]  [定时任务(1b2ce)] 开始执行任务：日常任务  
[2025-06-05 09:35:36.7822][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:35:36.7822][ INFO]  [定时任务(1b2ce)] 已确认在庭院场景  
[2025-06-05 09:35:36.7989][ INFO]  [定时任务(1b2ce)] 点击打开商店  
[2025-06-05 09:35:37.6669][ INFO] 任务 [大号_1b2ce] 已运行 120 秒，超时阈值：3600 秒  
[2025-06-05 09:35:39.6914][ INFO]  [定时任务(1b2ce)] 切换到礼包屋  
[2025-06-05 09:35:42.1695][ INFO]  [定时任务(1b2ce)] 打开推荐  
[2025-06-05 09:35:44.6460][ INFO]  [定时任务(1b2ce)] 检查是否可以领取免费礼包  
[2025-06-05 09:35:44.9680][ INFO]  [定时任务(1b2ce)] 检测到可领取的免费礼包，开始领取  
[2025-06-05 09:35:47.5813][ INFO]  [定时任务(1b2ce)] 礼包领取完成，已截图，点击随机范围  
[2025-06-05 09:35:50.3776][ INFO]  [定时任务(1b2ce)] 点击返回按钮  
[2025-06-05 09:35:53.3745][ INFO]  [定时任务(1b2ce)] 再次点击返回按钮  
[2025-06-05 09:35:55.5680][ INFO]  [定时任务(1b2ce)] 免费礼包任务完成  
[2025-06-05 09:35:57.6677][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-寮30交材料！  
[2025-06-05 09:35:58.7790][ INFO]  [定时任务(1b2ce)] 任务：[寮30] 重启游戏等事件监听已开启...  
[2025-06-05 09:35:59.8971][ INFO]  [定时任务(1b2ce)] 开始执行任务：日常任务  
[2025-06-05 09:35:59.9882][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:35:59.9882][ INFO]  [定时任务(1b2ce)] 已确认在庭院场景  
[2025-06-05 09:35:59.9882][ INFO]  [定时任务(1b2ce)] 点击进入阴阳寮..  
[2025-06-05 09:36:03.8535][DEBUG] 场景识别成功：阴阳寮  
[2025-06-05 09:36:05.7429][ INFO]  [定时任务(1b2ce)] 等待集体任务界面...  
[2025-06-05 09:36:10.2229][ INFO]  [定时任务(1b2ce)] 通过简单的方式拉满觉醒或预览材料...  
[2025-06-05 09:36:17.6756][ INFO]  [定时任务(1b2ce)] 点击提交  
[2025-06-05 09:36:21.7623][ INFO]  [定时任务(1b2ce)] 点击关闭提交界面  
[2025-06-05 09:36:23.2719][ INFO]  [定时任务(1b2ce)] 退出阴阳寮...结束寮提交材料任务..  
[2025-06-05 09:36:28.3280][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-悬赏！  
[2025-06-05 09:36:29.3517][ INFO]  [定时任务(1b2ce)] 任务：[悬赏] 重启游戏等事件监听已开启...  
[2025-06-05 09:36:30.4657][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:36:31.8739][ INFO]  [定时任务(1b2ce)] 判断是否打开了悬赏封印界面...  
[2025-06-05 09:36:32.3534][ INFO]  [定时任务(1b2ce)] 点击一键追踪...  
[2025-06-05 09:36:34.4037][ INFO]  [定时任务(1b2ce)] 关闭悬赏封印...并去探索场景..  
[2025-06-05 09:36:36.0166][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:36:36.0166][DEBUG]  [定时任务(1b2ce)] [TanSuo] 当前场景:庭院  
[2025-06-05 09:36:36.1043][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:36:36.1918][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:36:36.1918][ INFO]  [定时任务(1b2ce)] 滑动一下屏幕.  
[2025-06-05 09:36:37.6691][ INFO] 任务 [大号_1b2ce] 已运行 180 秒，超时阈值：3600 秒  
[2025-06-05 09:36:38.4368][ INFO]  [定时任务(1b2ce)] 点击进入探索.  
[2025-06-05 09:36:42.7154][DEBUG] 场景识别成功：探索  
[2025-06-05 09:36:42.8026][DEBUG] 场景识别成功：探索  
[2025-06-05 09:36:42.8026][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:36:45.4919][ERROR] OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..  
[2025-06-05 09:36:46.9696][ INFO]  [定时任务(1b2ce)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:36:46.9968][ INFO]  [定时任务(1b2ce)] 打开一个悬赏任务..  
[2025-06-05 09:36:48.3408][ INFO]  [定时任务(1b2ce)] 判断任务类型..  
[2025-06-05 09:36:48.9426][ INFO]  [定时任务(1b2ce)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:36:48.9662][ INFO]  [定时任务(1b2ce)] 任务类型：探索，点击前往..  
[2025-06-05 09:36:55.9083][ INFO]  [定时任务(1b2ce)] 点击探索  
[2025-06-05 09:36:59.4404][ INFO]  [定时任务(1b2ce)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:37:00.4969][ WARN]  [定时任务(1b2ce)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:37:01.5702][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:37:01.6426][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:37:02.9796][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:37:11.4010][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:37:12.7813][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:37:13.8139][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:37:15.1217][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:37:15.1224][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 09:37:17.3210][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:37:17.3917][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:37:18.9026][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:37:27.4850][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:37:29.2796][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:37:30.4059][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:37:31.7613][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:37:31.7937][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 09:37:33.9778][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:37:37.6721][ INFO] 任务 [大号_1b2ce] 已运行 240 秒，超时阈值：3600 秒  
[2025-06-05 09:37:37.9778][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:37:38.0514][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:37:39.9564][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:37:48.5124][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:37:50.2333][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:37:51.2729][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:37:52.4804][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:37:52.4804][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 09:37:54.5902][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:37:54.6618][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:37:56.0118][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:38:04.7159][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:38:06.0864][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:38:07.1167][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:38:08.2649][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:38:08.2649][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 09:38:10.4245][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:38:10.4517][ INFO]  [定时任务(1b2ce)] 找到Boss,准备战斗..  
[2025-06-05 09:38:11.7369][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:38:20.3240][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:38:21.5682][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:38:22.6239][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:38:23.9460][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:38:23.9817][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 09:38:24.0025][ INFO]  [定时任务(1b2ce)] 当前Boss战斗次数：1/1  
[2025-06-05 09:38:26.2395][DEBUG] 场景识别成功：探索  
[2025-06-05 09:38:28.3755][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:38:36.6958][DEBUG] 场景识别成功：探索  
[2025-06-05 09:38:36.6958][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:38:37.6744][ INFO] 任务 [大号_1b2ce] 已运行 300 秒，超时阈值：3600 秒  
[2025-06-05 09:38:38.2051][ INFO]  [定时任务(1b2ce)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:38:38.2322][ INFO]  [定时任务(1b2ce)] 打开一个悬赏任务..  
[2025-06-05 09:38:40.1629][ INFO]  [定时任务(1b2ce)] 判断任务类型..  
[2025-06-05 09:38:40.7890][ INFO]  [定时任务(1b2ce)] 判断最优的任务类型为：挑战 ???  
[2025-06-05 09:38:40.8104][ INFO]  [定时任务(1b2ce)] 任务类型：挑战，点击前往..  
[2025-06-05 09:38:47.7903][ INFO]  [定时任务(1b2ce)] 点击挑战  
[2025-06-05 09:38:50.2684][ INFO]  [定时任务(1b2ce)] 等待战斗结果..  
[2025-06-05 09:38:50.2684][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:39:07.8545][ INFO]  [定时任务(1b2ce)] 等待返回...  
[2025-06-05 09:39:10.0309][DEBUG] 场景识别成功：探索  
[2025-06-05 09:39:11.2528][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:39:19.7360][DEBUG] 场景识别成功：探索  
[2025-06-05 09:39:19.7360][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:39:21.2631][ INFO]  [定时任务(1b2ce)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:39:21.2631][ INFO]  [定时任务(1b2ce)] 打开一个悬赏任务..  
[2025-06-05 09:39:23.1748][ INFO]  [定时任务(1b2ce)] 判断任务类型..  
[2025-06-05 09:39:23.8147][ INFO]  [定时任务(1b2ce)] 判断最优的任务类型为：挑战 ???  
[2025-06-05 09:39:23.8147][ INFO]  [定时任务(1b2ce)] 任务类型：挑战，点击前往..  
[2025-06-05 09:39:30.7453][ INFO]  [定时任务(1b2ce)] 点击挑战  
[2025-06-05 09:39:33.2245][ INFO]  [定时任务(1b2ce)] 等待战斗结果..  
[2025-06-05 09:39:33.2476][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:39:37.6789][ INFO] 任务 [大号_1b2ce] 已运行 360 秒，超时阈值：3600 秒  
[2025-06-05 09:39:51.9255][ INFO]  [定时任务(1b2ce)] 等待返回...  
[2025-06-05 09:39:54.0991][DEBUG] 场景识别成功：探索  
[2025-06-05 09:39:55.4202][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:40:03.9709][DEBUG] 场景识别成功：探索  
[2025-06-05 09:40:03.9709][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:40:05.5143][ INFO]  [定时任务(1b2ce)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:40:05.5143][ INFO]  [定时任务(1b2ce)] 打开一个悬赏任务..  
[2025-06-05 09:40:07.4907][ INFO]  [定时任务(1b2ce)] 判断任务类型..  
[2025-06-05 09:40:08.0997][ INFO]  [定时任务(1b2ce)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:40:08.1211][ INFO]  [定时任务(1b2ce)] 任务类型：探索，点击前往..  
[2025-06-05 09:40:14.9665][ INFO]  [定时任务(1b2ce)] 点击探索  
[2025-06-05 09:40:18.3413][ INFO]  [定时任务(1b2ce)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:40:19.4368][ WARN]  [定时任务(1b2ce)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:40:20.4839][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:40:20.5551][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:40:22.0241][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:40:30.5902][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:40:32.2189][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:40:33.3022][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:40:34.5301][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:40:34.5563][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 09:40:36.7144][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:40:36.7825][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:40:37.6809][ INFO] 任务 [大号_1b2ce] 已运行 420 秒，超时阈值：3600 秒  
[2025-06-05 09:40:38.1856][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:40:46.7328][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:40:48.1360][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:40:49.1782][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:40:50.5813][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:40:50.5813][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 09:40:52.7473][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:40:52.8197][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:40:54.1821][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:41:02.8311][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:41:04.3318][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:41:05.3712][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:41:06.6806][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:41:06.7177][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 09:41:08.8624][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:41:08.8887][ INFO]  [定时任务(1b2ce)] 找到Boss,准备战斗..  
[2025-06-05 09:41:10.1916][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:41:18.7611][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:41:20.1007][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:41:21.1816][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:41:22.3852][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:41:22.3852][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%  
[2025-06-05 09:41:22.4205][ INFO]  [定时任务(1b2ce)] 当前Boss战斗次数：1/1  
[2025-06-05 09:41:30.4046][DEBUG] 场景识别成功：探索  
[2025-06-05 09:41:32.6095][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:41:37.6861][ INFO] 任务 [大号_1b2ce] 已运行 480 秒，超时阈值：3600 秒  
[2025-06-05 09:41:40.8545][DEBUG] 场景识别成功：探索  
[2025-06-05 09:41:40.8545][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:41:42.3435][ INFO]  [定时任务(1b2ce)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:41:42.3732][ INFO]  [定时任务(1b2ce)] 打开一个悬赏任务..  
[2025-06-05 09:41:44.3426][ INFO]  [定时任务(1b2ce)] 判断任务类型..  
[2025-06-05 09:41:44.9628][ INFO]  [定时任务(1b2ce)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:41:44.9862][ INFO]  [定时任务(1b2ce)] 任务类型：探索，点击前往..  
[2025-06-05 09:41:51.9384][ INFO]  [定时任务(1b2ce)] 点击探索  
[2025-06-05 09:41:55.4873][ INFO]  [定时任务(1b2ce)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:41:56.5296][ WARN]  [定时任务(1b2ce)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:41:57.6382][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:41:57.7065][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:41:59.0955][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:42:07.5985][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:42:09.3178][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:42:10.3653][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:42:11.6988][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:42:11.6988][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%  
[2025-06-05 09:42:13.8480][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:42:13.9182][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:42:15.3985][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:42:23.9866][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:42:25.4971][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:42:26.6089][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:42:27.9538][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:42:27.9538][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%  
[2025-06-05 09:42:30.1203][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:42:30.1874][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:42:31.6442][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:42:37.6893][ INFO] 任务 [大号_1b2ce] 已运行 540 秒，超时阈值：3600 秒  
[2025-06-05 09:42:40.3105][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:42:41.8890][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:42:43.0047][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:42:44.4562][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:42:44.4856][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%  
[2025-06-05 09:42:46.7111][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:42:50.7864][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:42:50.8562][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:42:52.8394][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:43:12.1731][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:43:13.9428][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:43:15.0478][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:43:16.3205][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:43:16.3479][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%  
[2025-06-05 09:43:18.4064][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:43:18.4749][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:43:19.9766][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:43:37.6917][ INFO] 任务 [大号_1b2ce] 已运行 600 秒，超时阈值：3600 秒  
[2025-06-05 09:43:39.4441][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:43:41.0225][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:43:42.1443][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:43:43.3815][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:43:43.3815][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%  
[2025-06-05 09:43:45.5157][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:43:45.5869][ INFO]  [定时任务(1b2ce)] 找到小怪,准备战斗..  
[2025-06-05 09:43:47.0179][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:43:55.6507][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:43:57.1418][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:43:58.2112][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:43:59.4094][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:43:59.4094][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%  
[2025-06-05 09:44:01.5254][DEBUG]  [定时任务(1b2ce)] 检查自动轮换是否开启..  
[2025-06-05 09:44:01.5516][ INFO]  [定时任务(1b2ce)] 找到Boss,准备战斗..  
[2025-06-05 09:44:02.8474][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:44:11.3511][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:44:12.5823][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:44:13.6254][ INFO]  [定时任务(1b2ce)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:44:14.9718][ INFO]  [定时任务(1b2ce)] 探索战斗胜利  
[2025-06-05 09:44:15.0006][DEBUG]  [定时任务(1b2ce)] 未触发随机延迟，当前触发概率已提升至：13.22%，下次增量：6.5684%  
[2025-06-05 09:44:15.0006][ INFO]  [定时任务(1b2ce)] 当前Boss战斗次数：1/1  
[2025-06-05 09:44:19.5649][ INFO]  [定时任务(1b2ce)] 尝试领取奖励..  
[2025-06-05 09:44:28.9866][ INFO]  [定时任务(1b2ce)] 悬赏任务清理完毕，结束任务..  
[2025-06-05 09:44:31.0470][ INFO]  [定时任务(1b2ce)] 开始执行日常任务-地域鬼王！  
[2025-06-05 09:44:32.1219][ INFO]  [定时任务(1b2ce)] 任务：[地鬼] 重启游戏等事件监听已开启...  
[2025-06-05 09:44:33.2138][ INFO]  [定时任务(1b2ce)] 前往地鬼任务场景..  
[2025-06-05 09:44:33.3072][DEBUG] 场景识别成功：探索  
[2025-06-05 09:44:33.3072][DEBUG]  [定时任务(1b2ce)] [TanSuo] 当前场景:探索  
[2025-06-05 09:44:33.4079][DEBUG] 场景识别成功：探索  
[2025-06-05 09:44:37.3305][ INFO]  [定时任务(1b2ce)] 尝试获取地鬼声望点数..  
[2025-06-05 09:44:37.6962][ INFO] 任务 [大号_1b2ce] 已运行 660 秒，超时阈值：3600 秒  
[2025-06-05 09:44:37.9851][ INFO]  [定时任务(1b2ce)] 当前声望：15485,支持战斗3个地域鬼王  
[2025-06-05 09:44:37.9851][ INFO]  [定时任务(1b2ce)] 现在开始选择鬼王并战斗..优先选择收藏中的鬼王..  
[2025-06-05 09:44:43.2898][ INFO]  [定时任务(1b2ce)] 开始第1个鬼王战斗..点击挑战..  
[2025-06-05 09:44:44.5722][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:44:46.6901][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_准备  
[2025-06-05 09:45:14.5741][DEBUG] 添加任务历史记录：日常任务1，ID：bb3ed8dc-fa84-4423-9250-3a41e421d1db  
[2025-06-05 09:45:15.6142][DEBUG] 已发送启动命令给模拟器，索引: 1，正在等待模拟器完全启动...  
[2025-06-05 09:45:16.6620][DEBUG] 模拟器已完全启动成功，索引: 1，main_wnd和render_wnd均已就绪  
[2025-06-05 09:45:16.6620][DEBUG] 成功解析窗口句柄 - 主窗口: 0x38091E, 渲染窗口: 0x90A30  
[2025-06-05 09:45:18.6629][ INFO] 成功获取模拟器和游戏句柄: MumuHandle=3672350, GameHandle=592432  
[2025-06-05 09:45:25.7040][DEBUG] 成功获取到模拟器(索引:1)的ADB端口: 16416  
[2025-06-05 09:45:25.7040][DEBUG] 提供了ADB端口参数 16416，尝试自动连接  
[2025-06-05 09:45:25.7543][DEBUG] ADB连接命令执行成功，正在检查设备状态...  
[2025-06-05 09:45:26.8002][DEBUG] 设备 127.0.0.1:16416 状态: device  
[2025-06-05 09:45:26.8002][DEBUG] 成功连接到ADB设备端口 16416，设备状态正常  
[2025-06-05 09:45:26.8447][DEBUG] 设备 127.0.0.1:16416 状态: device  
[2025-06-05 09:45:27.0764][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"
  
[2025-06-05 09:45:28.5545][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_胜利  
[2025-06-05 09:45:30.1929][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:45:31.2213][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_达摩  
[2025-06-05 09:45:32.1865][ INFO] 已启动阴阳师应用  
[2025-06-05 09:45:33.2366][ INFO] 图库初始化中. 图库源：本地，图库版本：1.4.19  
[2025-06-05 09:45:33.2366][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19  
[2025-06-05 09:45:33.2592][DEBUG] Main_Pics 被更新！版本号：1.4.19  
[2025-06-05 09:45:33.2592][ INFO] 图库初始化完成，图库版本：1.4.19 载入状态：False 图库数量：673 字库数量：25  
[2025-06-05 09:45:33.2592][ INFO] 初始化大漠构建器成功  
[2025-06-05 09:45:33.5697][DEBUG]  [定时任务(5e3e1)] 任务线程已经在后台执行，UI控制权转移给用户！  
[2025-06-05 09:45:33.5697][ INFO] 任务'日常任务1'启动成功  
[2025-06-05 09:45:33.5697][ INFO] 开始监控任务 [小号_5e3e1] 执行超时，最长执行时间：3600秒  
[2025-06-05 09:45:33.5697][DEBUG]  [定时任务(5e3e1)] 后台线程开启！  
[2025-06-05 09:45:33.5697][ INFO]  [定时任务(5e3e1)] 欢迎使用蛋定助手，遇到异常、停止请蛋定，仔细查看帮助链接，请确保您的模拟器设置符合要求！
若有意料之外的错误，您可以加群(621016172)咨询管理员，谢谢！
5S后将继续执行，再说一遍：请确保您模拟器的设置符合我们的要求(分辨率、渲染引擎···)！  
[2025-06-05 09:45:37.7012][ INFO] 任务 [大号_1b2ce] 已运行 720 秒，超时阈值：3600 秒  
[2025-06-05 09:45:38.5708][ INFO] 点击位置更新完成，更新模式: 胜利/失败[关闭], 达摩[关闭]  
[2025-06-05 09:45:38.5708][ INFO] 定时任务(5e3e1) 当前执行: 绑定游戏  
[2025-06-05 09:45:38.5708][ INFO]  [定时任务(5e3e1)] 句柄[592432],开始执行脚本流程！  
[2025-06-05 09:45:38.5708][ INFO]  [定时任务(5e3e1)] 主要对象开始绑定[主对象]  
[2025-06-05 09:45:38.6012][DEBUG]  [定时任务(5e3e1)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 09:45:39.8035][ INFO]  [定时任务(5e3e1)] 辅助对象开始绑定[检测对象]  
[2025-06-05 09:45:39.8035][DEBUG]  [定时任务(5e3e1)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 09:45:40.8620][DEBUG]  [定时任务(5e3e1)] dm_id已捕获：main[579294340] sub[579330316]  
[2025-06-05 09:45:41.9657][ INFO]  [定时任务(5e3e1)] 当前系统CPU：Intel Intel(R) Core(TM) i5-14600KF，显卡信息：NVIDIA GeForce RTX 4060 Ti，DPI是否为100%：1，系统OS版本：10.0.26100.3912 (WinBuild.160101.0800)  
[2025-06-05 09:45:42.4170][ INFO]  [定时任务(1b2ce)] 开始第2个鬼王战斗..点击挑战..  
[2025-06-05 09:45:43.6715][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:45:43.9688][DEBUG]  [定时任务(5e3e1)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭  
[2025-06-05 09:45:43.9688][DEBUG]  [定时任务(5e3e1)] 小纸人配置 - 基础触发概率: 0.00%  
[2025-06-05 09:45:44.9678][ INFO]  [定时任务(5e3e1)] 等待游戏到庭院后再启动主任务！  
[2025-06-05 09:45:45.0506][DEBUG]  [定时任务(5e3e1)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏  
[2025-06-05 09:45:45.8019][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_准备  
[2025-06-05 09:45:46.1067][ INFO]  [定时任务(5e3e1)] 开始将游戏场景操作至庭院！  
[2025-06-05 09:45:54.6514][ INFO]  [定时任务(5e3e1)] 当前默认的服务器为：2 人间千年(非准确)，点击进入游戏..  
[2025-06-05 09:45:55.9291][ INFO]  [定时任务(5e3e1)] 请确保您的卷轴皮肤为默认皮肤！..  
[2025-06-05 09:45:57.5921][ INFO]  [定时任务(5e3e1)] 关闭牛皮癣广告弹窗..  
[2025-06-05 09:46:01.3324][ INFO]  [定时任务(5e3e1)] 关闭牛皮癣广告弹窗..  
[2025-06-05 09:46:04.5258][ INFO]  [定时任务(5e3e1)] 重启恢复游戏场景完成，当前界面为庭院，为您继续完成任务！  
[2025-06-05 09:46:04.9776][ INFO]  [定时任务(5e3e1)] 开始分析依次执行任务清单！列表整体循环次数：1  
[2025-06-05 09:46:05.0041][DEBUG]  [定时任务(5e3e1)] 设置的庭院皮肤为：枫色秋庭  
[2025-06-05 09:46:05.0041][ INFO] 定时任务(5e3e1) 当前执行: 初始定时任务  
[2025-06-05 09:46:05.0326][ INFO]  [定时任务(5e3e1)] 开始执行第1次任务循环！  
[2025-06-05 09:46:05.0326][ INFO] 定时任务(5e3e1) 当前执行: 日常任务  
[2025-06-05 09:46:05.0605][ INFO]  [定时任务(5e3e1)] 开始执行任务：日常任务 1次  
[2025-06-05 09:46:06.1684][ INFO]  [定时任务(5e3e1)] 任务：[] 重启游戏等事件监听已开启...  
[2025-06-05 09:46:07.2869][ INFO]  [定时任务(5e3e1)] 开始执行日常任务-每日签到！  
[2025-06-05 09:46:08.3483][ INFO]  [定时任务(5e3e1)] 任务：[签到] 重启游戏等事件监听已开启...  
[2025-06-05 09:46:09.4038][ INFO]  [定时任务(5e3e1)] 开始执行任务：日常任务  
[2025-06-05 09:46:11.6295][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:11.6295][DEBUG]  [定时任务(5e3e1)] [TingYuan] 当前场景:庭院  
[2025-06-05 09:46:11.7238][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:11.7238][ INFO]  [定时任务(5e3e1)] 首次启动，庭院尚未初始化，进入图鉴初始化人物位置..  
[2025-06-05 09:46:20.1369][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:20.1369][ INFO]  [定时任务(5e3e1)] 已确认在庭院场景  
[2025-06-05 09:46:23.6935][ INFO]  [定时任务(5e3e1)] 点击签到完成..开奖..  
[2025-06-05 09:46:28.0387][ INFO]  [定时任务(5e3e1)] 截图->退出..  
[2025-06-05 09:46:28.1649][DEBUG] YoloClassfly：未知  
[2025-06-05 09:46:28.1649][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 09:46:30.7019][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:30.7019][ INFO]  [定时任务(5e3e1)] 每日签到任务完成  
[2025-06-05 09:46:30.9569][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_胜利  
[2025-06-05 09:46:32.7234][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:46:32.8075][ INFO]  [定时任务(5e3e1)] 开始执行日常任务-友情点！  
[2025-06-05 09:46:33.5725][ INFO] 任务 [小号_5e3e1] 已运行 60 秒，超时阈值：3600 秒  
[2025-06-05 09:46:33.7497][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_达摩  
[2025-06-05 09:46:33.8367][ INFO]  [定时任务(5e3e1)] 任务：[友情点] 重启游戏等事件监听已开启...  
[2025-06-05 09:46:34.9524][ INFO]  [定时任务(5e3e1)] 开始执行任务：日常任务  
[2025-06-05 09:46:35.0464][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:37.7045][ INFO] 任务 [大号_1b2ce] 已运行 780 秒，超时阈值：3600 秒  
[2025-06-05 09:46:39.7763][ERROR] OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..  
[2025-06-05 09:46:40.8065][ INFO]  [定时任务(5e3e1)] 未在好友界面，尝试打开好友界面  
[2025-06-05 09:46:44.7408][ INFO]  [定时任务(1b2ce)] 开始第3个鬼王战斗..点击挑战..  
[2025-06-05 09:46:45.2672][ INFO]  [定时任务(5e3e1)] 切换到友情点标签  
[2025-06-05 09:46:45.9834][ INFO]  [定时任务(1b2ce)] 战斗开始  
[2025-06-05 09:46:47.9084][ INFO]  [定时任务(5e3e1)] 检测到可领取的友情点，开始领取  
[2025-06-05 09:46:48.0946][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_准备  
[2025-06-05 09:46:50.1049][ INFO]  [定时任务(5e3e1)] 友情点领取完成  
[2025-06-05 09:46:54.3838][ INFO]  [定时任务(5e3e1)] 已关闭好友界面，结束任务..  
[2025-06-05 09:46:56.4906][ INFO]  [定时任务(5e3e1)] 开始执行日常任务-每日免费礼包！  
[2025-06-05 09:46:57.5334][ INFO]  [定时任务(5e3e1)] 任务：[礼包] 重启游戏等事件监听已开启...  
[2025-06-05 09:46:58.6515][ INFO]  [定时任务(5e3e1)] 开始执行任务：日常任务  
[2025-06-05 09:46:58.7469][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:46:58.7469][ INFO]  [定时任务(5e3e1)] 已确认在庭院场景  
[2025-06-05 09:46:58.7761][ INFO]  [定时任务(5e3e1)] 点击打开商店  
[2025-06-05 09:47:01.4539][ INFO]  [定时任务(5e3e1)] 切换到礼包屋  
[2025-06-05 09:47:03.9710][ INFO]  [定时任务(5e3e1)] 打开推荐  
[2025-06-05 09:47:06.4388][ INFO]  [定时任务(5e3e1)] 检查是否可以领取免费礼包  
[2025-06-05 09:47:06.7791][ INFO]  [定时任务(5e3e1)] 检测到可领取的免费礼包，开始领取  
[2025-06-05 09:47:09.5258][ INFO]  [定时任务(5e3e1)] 礼包领取完成，已截图，点击随机范围  
[2025-06-05 09:47:12.3761][ INFO]  [定时任务(5e3e1)] 点击返回按钮  
[2025-06-05 09:47:15.4691][ INFO]  [定时任务(5e3e1)] 再次点击返回按钮  
[2025-06-05 09:47:17.6834][ INFO]  [定时任务(5e3e1)] 免费礼包任务完成  
[2025-06-05 09:47:19.7675][ INFO]  [定时任务(5e3e1)] 开始执行日常任务-悬赏！  
[2025-06-05 09:47:20.8205][ INFO]  [定时任务(5e3e1)] 任务：[悬赏] 重启游戏等事件监听已开启...  
[2025-06-05 09:47:21.9722][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:47:23.3946][ INFO]  [定时任务(5e3e1)] 判断是否打开了悬赏封印界面...  
[2025-06-05 09:47:23.9268][ INFO]  [定时任务(5e3e1)] 点击一键追踪...  
[2025-06-05 09:47:25.7873][ INFO]  [定时任务(5e3e1)] 关闭悬赏封印...并去探索场景..  
[2025-06-05 09:47:27.4301][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:47:27.4301][DEBUG]  [定时任务(5e3e1)] [TanSuo] 当前场景:庭院  
[2025-06-05 09:47:27.5194][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:47:27.6133][DEBUG] 场景识别成功：庭院  
[2025-06-05 09:47:27.6133][ INFO]  [定时任务(5e3e1)] 滑动一下屏幕.  
[2025-06-05 09:47:29.7110][ INFO]  [定时任务(5e3e1)] 点击进入探索.  
[2025-06-05 09:47:33.5760][ INFO] 任务 [小号_5e3e1] 已运行 120 秒，超时阈值：3600 秒  
[2025-06-05 09:47:33.9757][DEBUG] 场景识别成功：探索  
[2025-06-05 09:47:34.0669][DEBUG] 场景识别成功：探索  
[2025-06-05 09:47:34.0669][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:47:36.6926][ERROR] OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..  
[2025-06-05 09:47:37.7093][ INFO] 任务 [大号_1b2ce] 已运行 840 秒，超时阈值：3600 秒  
[2025-06-05 09:47:38.3928][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:47:38.4187][ INFO]  [定时任务(5e3e1)] 目标任务是协同任务..请自行解决..  
[2025-06-05 09:47:38.4187][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:47:39.7123][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_胜利  
[2025-06-05 09:47:39.7468][ WARN]  [定时任务(5e3e1)] 未知的任务类型，取消追踪..  
[2025-06-05 09:47:41.3352][ INFO]  [定时任务(1b2ce)] 战斗胜利(Combat_End)..  
[2025-06-05 09:47:42.4074][ INFO]  [定时任务(1b2ce)] 执行点击：地鬼.ZD_达摩1  
[2025-06-05 09:47:48.3823][ INFO]  [定时任务(1b2ce)] 地鬼任务结束，退回到探索..  
[2025-06-05 09:47:49.7848][DEBUG] 场景识别成功：探索  
[2025-06-05 09:47:49.7848][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:47:51.5566][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:47:51.5825][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:47:53.4903][ INFO]  [定时任务(1b2ce)] 所有日常任务已完成！  
[2025-06-05 09:47:53.5169][ INFO]  [定时任务(1b2ce)] 脚本任务结束  
[2025-06-05 09:47:53.5757][ INFO]  [定时任务(5e3e1)] 判断任务类型..  
[2025-06-05 09:47:54.0625][DEBUG]  [定时任务(1b2ce)] 任务:[Sub]被取消..  
[2025-06-05 09:47:54.2160][ INFO]  [定时任务(5e3e1)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:47:54.2426][ INFO]  [定时任务(5e3e1)] 任务类型：探索，点击前往..  
[2025-06-05 09:47:54.7612][ INFO] 定时任务已完成，点击次数：152，执行任务：日常任务-1次  
[2025-06-05 09:47:54.7612][ INFO] 清理任务 [大号_1b2ce] 的执行信息和超时监控  
[2025-06-05 09:47:54.7612][ INFO] 任务执行时长：14.29分钟  
[2025-06-05 09:47:55.0953][ INFO] 脚本任务 [大号_1b2ce] 已完成，已从跟踪列表中移除  
[2025-06-05 09:47:55.0953][DEBUG] 更新任务历史记录状态：21c5e7b4-a21a-4a96-ab7a-e60ec31f26a7，新状态：成功  
[2025-06-05 09:47:55.2169][ INFO] [App] 通知发送成功  
[2025-06-05 09:47:55.3448][ INFO] [微信推送] 通知发送成功  
[2025-06-05 09:47:55.3448][ INFO] 成功发送通知类型: App, 微信推送  
[2025-06-05 09:47:55.3448][ INFO] 任务结束通知发送成功！执行时长：14.29分钟  
[2025-06-05 09:47:55.3448][ INFO] 正在关闭模拟器...  
[2025-06-05 09:47:55.5655][DEBUG] 已发送关闭命令给模拟器，索引: 0  
[2025-06-05 09:47:55.5845][ INFO] 模拟器已关闭  
[2025-06-05 09:47:57.7103][ INFO] 任务 [大号_1b2ce] 的执行信息已不存在，停止超时监控  
[2025-06-05 09:48:01.1436][ INFO]  [定时任务(5e3e1)] 点击探索  
[2025-06-05 09:48:04.6300][ INFO]  [定时任务(5e3e1)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:48:05.6644][ WARN]  [定时任务(5e3e1)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:48:06.7516][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:48:06.8246][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:48:08.2935][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:48:16.6933][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:48:17.9403][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:48:19.0402][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:48:20.3149][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:48:20.3149][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 09:48:22.4524][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:48:22.5214][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:48:23.8411][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:48:32.2757][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:48:33.5781][ INFO] 任务 [小号_5e3e1] 已运行 180 秒，超时阈值：3600 秒  
[2025-06-05 09:48:33.7121][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:48:34.8118][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:48:36.0235][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:48:36.0525][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 09:48:38.1602][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:48:42.3302][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:48:42.3996][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:48:44.2126][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:48:57.2151][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:48:58.7886][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:48:59.9077][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:49:01.1520][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:49:01.1798][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 09:49:03.3330][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:49:03.4047][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:49:04.9450][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:49:18.0146][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:49:19.6318][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:49:20.7404][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:49:21.9690][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:49:21.9962][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 09:49:24.1526][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:49:24.1817][ INFO]  [定时任务(5e3e1)] 找到Boss,准备战斗..  
[2025-06-05 09:49:25.4055][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:49:33.5813][ INFO] 任务 [小号_5e3e1] 已运行 240 秒，超时阈值：3600 秒  
[2025-06-05 09:49:48.8426][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:49:50.0982][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:49:51.1486][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:49:52.4892][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:49:52.5168][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 09:49:52.5424][ INFO]  [定时任务(5e3e1)] 当前Boss战斗次数：1/1  
[2025-06-05 09:49:59.6655][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:50:02.3940][DEBUG] 场景识别成功：探索  
[2025-06-05 09:50:02.3940][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:50:03.9432][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:50:03.9688][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:50:05.8373][ INFO]  [定时任务(5e3e1)] 判断任务类型..  
[2025-06-05 09:50:06.4275][ INFO]  [定时任务(5e3e1)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:50:06.4275][ INFO]  [定时任务(5e3e1)] 任务类型：探索，点击前往..  
[2025-06-05 09:50:13.3960][ INFO]  [定时任务(5e3e1)] 点击探索  
[2025-06-05 09:50:16.9516][ INFO]  [定时任务(5e3e1)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:50:18.0272][ WARN]  [定时任务(5e3e1)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:50:19.0807][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:50:19.1528][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:50:20.5222][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:50:29.1359][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:50:30.5674][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:50:31.6079][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:50:33.1042][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:50:33.1361][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 09:50:33.5841][ INFO] 任务 [小号_5e3e1] 已运行 300 秒，超时阈值：3600 秒  
[2025-06-05 09:50:35.2616][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:50:35.3312][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:50:36.7603][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:50:45.4290][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:50:47.0130][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:50:48.0515][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:50:49.3120][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:50:49.3452][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 09:50:51.4077][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:50:55.3260][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:50:55.3949][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:50:57.2465][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:51:10.1874][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:51:11.8527][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:51:12.9109][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:51:14.3098][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:51:14.3451][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 09:51:16.4162][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:51:16.4850][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:51:17.7689][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:51:30.6039][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:51:32.2156][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:51:33.2899][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:51:33.5867][ INFO] 任务 [小号_5e3e1] 已运行 360 秒，超时阈值：3600 秒  
[2025-06-05 09:51:34.7235][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:51:34.7524][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%  
[2025-06-05 09:51:36.8341][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:51:36.9049][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:51:38.1705][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:51:46.8104][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:51:48.3701][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:51:49.4946][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:51:50.8522][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:51:50.8827][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%  
[2025-06-05 09:51:53.0384][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:51:57.0988][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:51:57.1721][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:51:58.7442][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:52:11.5470][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:52:12.9175][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:52:13.9695][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:52:15.3816][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:52:15.4112][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%  
[2025-06-05 09:52:17.5902][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:52:17.6592][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:52:19.1103][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:52:28.8097][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:52:30.3911][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:52:31.5015][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:52:32.8944][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:52:32.8944][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%  
[2025-06-05 09:52:33.5901][ INFO] 任务 [小号_5e3e1] 已运行 420 秒，超时阈值：3600 秒  
[2025-06-05 09:52:35.0365][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:52:35.0685][ INFO]  [定时任务(5e3e1)] 找到Boss,准备战斗..  
[2025-06-05 09:52:36.2913][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:52:59.7840][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:53:00.9776][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:53:02.0471][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩1  
[2025-06-05 09:53:03.3859][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:53:03.4168][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%  
[2025-06-05 09:53:03.4420][ INFO]  [定时任务(5e3e1)] 当前Boss战斗次数：1/1  
[2025-06-05 09:53:10.4963][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:53:19.0565][DEBUG] 场景识别成功：探索  
[2025-06-05 09:53:19.0565][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:53:20.6330][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:53:20.6330][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:53:22.6608][ INFO]  [定时任务(5e3e1)] 判断任务类型..  
[2025-06-05 09:53:23.2603][ INFO]  [定时任务(5e3e1)] 判断最优的任务类型为：挑战 ???  
[2025-06-05 09:53:23.2855][ INFO]  [定时任务(5e3e1)] 任务类型：挑战，点击前往..  
[2025-06-05 09:53:30.3006][ INFO]  [定时任务(5e3e1)] 点击挑战  
[2025-06-05 09:53:32.7482][ INFO]  [定时任务(5e3e1)] 等待战斗结果..  
[2025-06-05 09:53:32.7774][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:53:33.5940][ INFO] 任务 [小号_5e3e1] 已运行 480 秒，超时阈值：3600 秒  
[2025-06-05 09:54:02.0699][ INFO]  [定时任务(5e3e1)] 等待返回...  
[2025-06-05 09:54:04.2446][DEBUG] 场景识别成功：探索  
[2025-06-05 09:54:05.4133][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:54:13.9251][DEBUG] 场景识别成功：探索  
[2025-06-05 09:54:13.9251][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:54:15.3491][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:54:15.3741][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:54:17.3310][ INFO]  [定时任务(5e3e1)] 判断任务类型..  
[2025-06-05 09:54:17.9296][ INFO]  [定时任务(5e3e1)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:54:17.9546][ INFO]  [定时任务(5e3e1)] 任务类型：探索，点击前往..  
[2025-06-05 09:54:24.8867][ INFO]  [定时任务(5e3e1)] 点击探索  
[2025-06-05 09:54:28.4811][ INFO]  [定时任务(5e3e1)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:54:29.5192][ WARN]  [定时任务(5e3e1)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:54:30.5558][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:54:30.6262][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:54:31.8678][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:54:33.5971][ INFO] 任务 [小号_5e3e1] 已运行 540 秒，超时阈值：3600 秒  
[2025-06-05 09:54:40.4313][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:54:41.8722][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:54:42.9376][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:54:44.3440][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:54:44.3761][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%  
[2025-06-05 09:54:46.5190][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:54:46.5912][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:54:48.0123][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:54:56.5653][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:54:58.1018][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:54:59.2016][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:55:00.4605][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:55:00.4903][ INFO]  [定时任务(5e3e1)] 触发随机延迟等待，当前触发概率：5.92%，等待时长：32秒  
[2025-06-05 09:55:33.6002][ INFO] 任务 [小号_5e3e1] 已运行 600 秒，超时阈值：3600 秒  
[2025-06-05 09:55:34.7487][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:55:38.7917][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:55:38.8648][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:55:40.4967][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:55:49.0394][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:55:50.3056][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:55:51.3808][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:55:52.7229][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:55:52.7511][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 09:55:54.8425][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:55:54.8683][ INFO]  [定时任务(5e3e1)] 找到Boss,准备战斗..  
[2025-06-05 09:55:56.1104][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:56:33.6031][ INFO] 任务 [小号_5e3e1] 已运行 660 秒，超时阈值：3600 秒  
[2025-06-05 09:56:37.0740][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:56:38.2889][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:56:39.3973][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:56:40.5784][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:56:40.6055][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 09:56:40.6055][ INFO]  [定时任务(5e3e1)] 当前Boss战斗次数：1/1  
[2025-06-05 09:56:42.7850][DEBUG] 场景识别成功：探索  
[2025-06-05 09:56:44.9810][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:56:47.7018][DEBUG] 场景识别成功：探索  
[2025-06-05 09:56:47.7018][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:56:49.2785][ INFO]  [定时任务(5e3e1)] 判断悬赏任务是否为协同任务..  
[2025-06-05 09:56:49.3040][ INFO]  [定时任务(5e3e1)] 打开一个悬赏任务..  
[2025-06-05 09:56:50.8794][ INFO]  [定时任务(5e3e1)] 判断任务类型..  
[2025-06-05 09:56:51.4706][ INFO]  [定时任务(5e3e1)] 判断最优的任务类型为：探索 ???  
[2025-06-05 09:56:51.5011][ INFO]  [定时任务(5e3e1)] 任务类型：探索，点击前往..  
[2025-06-05 09:56:58.4363][ INFO]  [定时任务(5e3e1)] 点击探索  
[2025-06-05 09:57:01.9828][ INFO]  [定时任务(5e3e1)] 任务：[探索] 重启游戏等事件监听已开启...  
[2025-06-05 09:57:03.0715][ WARN]  [定时任务(5e3e1)] 当前场景在探索任务主界面，开始直接识别怪物并战斗！  
[2025-06-05 09:57:04.1631][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:57:04.2332][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:57:05.7124][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:57:14.2824][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:57:15.8878][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:57:16.9990][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:57:18.3514][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:57:18.3821][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 09:57:20.4865][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:57:24.4870][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:57:24.5615][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:57:26.0978][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:57:33.6064][ INFO] 任务 [小号_5e3e1] 已运行 720 秒，超时阈值：3600 秒  
[2025-06-05 09:57:34.7688][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:57:36.0719][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:57:37.1480][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:57:38.5576][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:57:38.5879][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 09:57:40.7352][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:57:40.8067][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:57:42.2486][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:57:50.9031][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:57:52.5884][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:57:53.6901][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:57:55.0444][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:57:55.0734][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 09:57:57.1972][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:57:57.2686][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:57:58.5877][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:58:07.1426][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:58:08.4737][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:58:09.5897][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:58:10.8517][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:58:10.8787][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 09:58:13.0698][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:58:13.1428][ INFO]  [定时任务(5e3e1)] 找到小怪,准备战斗..  
[2025-06-05 09:58:14.5766][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:58:23.3009][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:58:24.9692][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:58:26.0275][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:58:27.3206][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:58:27.3478][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 09:58:29.5132][DEBUG]  [定时任务(5e3e1)] 检查自动轮换是否开启..  
[2025-06-05 09:58:29.5444][ INFO]  [定时任务(5e3e1)] 找到Boss,准备战斗..  
[2025-06-05 09:58:30.7335][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:58:33.6097][ INFO] 任务 [小号_5e3e1] 已运行 780 秒，超时阈值：3600 秒  
[2025-06-05 09:59:06.2714][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_胜利  
[2025-06-05 09:59:07.4654][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 09:59:08.4980][ INFO]  [定时任务(5e3e1)] 执行点击：探索.战斗_达摩  
[2025-06-05 09:59:09.6926][ INFO]  [定时任务(5e3e1)] 探索战斗胜利  
[2025-06-05 09:59:09.6926][DEBUG]  [定时任务(5e3e1)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 09:59:09.7264][ INFO]  [定时任务(5e3e1)] 当前Boss战斗次数：1/1  
[2025-06-05 09:59:16.7764][ INFO]  [定时任务(5e3e1)] 尝试领取奖励..  
[2025-06-05 09:59:26.5969][ INFO]  [定时任务(5e3e1)] 悬赏任务清理完毕，结束任务..  
[2025-06-05 09:59:28.6267][ INFO]  [定时任务(5e3e1)] 开始执行日常任务-地域鬼王！  
[2025-06-05 09:59:29.7128][ INFO]  [定时任务(5e3e1)] 任务：[地鬼] 重启游戏等事件监听已开启...  
[2025-06-05 09:59:30.7870][ INFO]  [定时任务(5e3e1)] 前往地鬼任务场景..  
[2025-06-05 09:59:30.8794][DEBUG] 场景识别成功：探索  
[2025-06-05 09:59:30.8794][DEBUG]  [定时任务(5e3e1)] [TanSuo] 当前场景:探索  
[2025-06-05 09:59:30.9674][DEBUG] 场景识别成功：探索  
[2025-06-05 09:59:33.6125][ INFO] 任务 [小号_5e3e1] 已运行 840 秒，超时阈值：3600 秒  
[2025-06-05 09:59:34.8214][ INFO]  [定时任务(5e3e1)] 尝试获取地鬼声望点数..  
[2025-06-05 09:59:35.4832][ INFO]  [定时任务(5e3e1)] 当前声望：1020,支持战斗1个地域鬼王  
[2025-06-05 09:59:35.5087][ INFO]  [定时任务(5e3e1)] 现在开始选择鬼王并战斗..优先选择收藏中的鬼王..  
[2025-06-05 09:59:40.8193][ INFO]  [定时任务(5e3e1)] 开始第1个鬼王战斗..点击挑战..  
[2025-06-05 09:59:42.2529][ INFO]  [定时任务(5e3e1)] 战斗开始  
[2025-06-05 09:59:45.5076][ INFO]  [定时任务(5e3e1)] 执行点击：地鬼.ZD_准备  
[2025-06-05 10:00:27.4158][ INFO]  [定时任务(5e3e1)] 执行点击：地鬼.ZD_胜利  
[2025-06-05 10:00:29.1643][ INFO]  [定时任务(5e3e1)] 战斗胜利(Combat_End)..  
[2025-06-05 10:00:30.2089][ INFO]  [定时任务(5e3e1)] 执行点击：地鬼.ZD_达摩1  
[2025-06-05 10:00:33.6162][ INFO] 任务 [小号_5e3e1] 已运行 900 秒，超时阈值：3600 秒  
[2025-06-05 10:00:36.1704][ INFO]  [定时任务(5e3e1)] 地鬼任务结束，退回到探索..  
[2025-06-05 10:00:41.1516][ INFO]  [定时任务(5e3e1)] 所有日常任务已完成！  
[2025-06-05 10:00:41.1788][ INFO]  [定时任务(5e3e1)] 脚本任务结束  
[2025-06-05 10:00:41.5514][DEBUG]  [定时任务(5e3e1)] 任务:[Sub]被取消..  
[2025-06-05 10:00:42.4253][ INFO] 定时任务已完成，点击次数：148，执行任务：日常任务-1次  
[2025-06-05 10:00:42.4253][ INFO] 清理任务 [小号_5e3e1] 的执行信息和超时监控  
[2025-06-05 10:00:42.4253][ INFO] 任务执行时长：15.15分钟  
[2025-06-05 10:00:42.8314][ INFO] [App] 通知发送成功  
[2025-06-05 10:00:42.9518][ INFO] [微信推送] 通知发送成功  
[2025-06-05 10:00:42.9518][ INFO] 成功发送通知类型: App, 微信推送  
[2025-06-05 10:00:42.9518][ INFO] 任务结束通知发送成功！执行时长：15.15分钟  
[2025-06-05 10:00:42.9518][ INFO] 正在关闭模拟器...  
[2025-06-05 10:00:43.0132][ INFO] 脚本任务 [小号_5e3e1] 已完成，已从跟踪列表中移除  
[2025-06-05 10:00:43.0132][DEBUG] 更新任务历史记录状态：bb3ed8dc-fa84-4423-9250-3a41e421d1db，新状态：成功  
[2025-06-05 10:00:43.0878][DEBUG] 已发送关闭命令给模拟器，索引: 1  
[2025-06-05 10:00:43.1200][ INFO] 模拟器已关闭  
[2025-06-05 10:00:43.6160][ INFO] 任务 [小号_5e3e1] 的执行信息已不存在，停止超时监控  
[2025-06-05 10:21:12.8111][DEBUG] CheckUserStatus|Running...  
[2025-06-05 10:21:13.0116][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 10:23:04.5995][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 10:23:04.5995][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 10:23:04.5995][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 10:23:04.6092][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共3条  
[2025-06-05 10:23:04.6092][DEBUG] 已更新分页，当前第1/1页，每页50条，共3条记录  
[2025-06-05 10:23:04.6092][DEBUG] 应用过滤器后，显示3/3条记录  
[2025-06-05 10:23:04.6092][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共3条  
[2025-06-05 10:23:04.6092][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共3条  
[2025-06-05 10:23:04.6092][DEBUG] 已刷新统计信息  
[2025-06-05 10:23:04.6092][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 10:23:18.5835][DEBUG] 已停止自动刷新  
[2025-06-05 10:23:18.5835][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止  
[2025-06-05 10:23:20.7948][ INFO] 正在退出应用程序...  
[2025-06-05 10:23:20.7948][ INFO] 已停止所有运行中的脚本任务，模拟器保持运行状态  
[2025-06-05 10:23:20.7948][ INFO] 正在清理调度器资源...  
[2025-06-05 10:23:20.7948][ INFO] 调度器资源已清理完成，模拟器保持运行状态  
[2025-06-05 10:23:20.7948][ INFO] 应用程序即将关闭，模拟器将继续运行  
[2025-06-05 10:23:20.8102][ INFO] 托盘提示更新定时器已停止  
[2025-06-05 10:23:20.8102][ INFO] 托盘图标资源已释放  
[2025-06-05 10:23:20.8102][ INFO] 应用退出时已释放托盘图标资源  
[2025-06-05 10:23:20.8376][ INFO] AppConfig配置文件已成功备份到: C:\Users\<USER>\AppData\Roaming\DanDing1\AppConfig  
[2025-06-05 12:32:25.9571][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:25.9571][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:25.9571][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 12:32:25.9571][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 12:32:25.9571][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 12:32:25.9728][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 12:32:26.1733][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:26.3754][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 12:32:26.3754][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 12:32:26.4802][ INFO] 正在初始化后台调度器...  
[2025-06-05 12:32:26.4927][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 12:32:26.6009][ INFO] 已设置调度器视图模型实例  
[2025-06-05 12:32:26.6009][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 12:32:26.7500][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 12:32:26.7522][ INFO]   
[2025-06-05 12:32:26.7522][ INFO] =========DanDing-Started==========  
[2025-06-05 12:32:26.7522][ INFO]   
[2025-06-05 12:32:26.7522][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 12:32:28.7001][ INFO] 调度器已在后台启动完成  
[2025-06-05 12:32:29.1747][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 12:32:30.4638][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 12:32:31.0512][DEBUG] WebSocket连接成功  
[2025-06-05 12:32:31.0938][DEBUG] 常规消息，type=welcome  
[2025-06-05 12:32:31.0938][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1133","clientId":"xsllovemlj-client-1133","username":"xsllovemlj","timestamp":1749097880142}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 12:32:31.0954][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 12:32:31.0954][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:31.0954][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:31.0954][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 12:32:31.0954][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 12:32:32.9935][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 12:32:32.9935][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 12:32:41.1891][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：16384  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：16416  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：0  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：0  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：0  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：0  
[2025-06-05 12:32:41.1910][ERROR] MuMuConfigsWindow: 从配置文件中读取adb端口：16576  
[2025-06-05 12:37:27.9991][DEBUG] CheckUserStatus|Running...  
[2025-06-05 12:37:28.2938][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 13:32:00.9000][DEBUG] 添加任务历史记录：契灵50，ID：b485f870-8808-4971-baf0-7129cfa03341  
[2025-06-05 13:32:01.9603][DEBUG] 已发送启动命令给模拟器，索引: 0，正在等待模拟器完全启动...  
[2025-06-05 13:32:03.0048][DEBUG] 模拟器已完全启动成功，索引: 0，main_wnd和render_wnd均已就绪  
[2025-06-05 13:32:03.0048][DEBUG] 成功解析窗口句柄 - 主窗口: 0x6E0AC8, 渲染窗口: 0x540ACE  
[2025-06-05 13:32:05.0068][ INFO] 成功获取模拟器和游戏句柄: MumuHandle=7211720, GameHandle=5507790  
[2025-06-05 13:32:12.0532][DEBUG] 成功获取到模拟器(索引:0)的ADB端口: 16384  
[2025-06-05 13:32:12.0532][DEBUG] 提供了ADB端口参数 16384，尝试自动连接  
[2025-06-05 13:32:12.1110][DEBUG] ADB连接命令执行成功，正在检查设备状态...  
[2025-06-05 13:32:13.1878][DEBUG] 设备 127.0.0.1:16384 状态: device  
[2025-06-05 13:32:13.1878][DEBUG] 成功连接到ADB设备端口 16384，设备状态正常  
[2025-06-05 13:32:13.2383][DEBUG] 设备 127.0.0.1:16384 状态: device  
[2025-06-05 13:32:13.4832][DEBUG] 启动应用 com.netease.onmyoji.wyzymnqsd_cps 失败: args: [-p, com.netease.onmyoji.wyzymnqsd_cps, -c, android.intent.category.LAUNCHER, --pct-syskeys, 0, 1]
 arg: "-p"
 arg: "com.netease.onmyoji.wyzymnqsd_cps"
 arg: "-c"
 arg: "android.intent.category.LAUNCHER"
 arg: "--pct-syskeys"
 arg: "0"
 arg: "1"
data="com.netease.onmyoji.wyzymnqsd_cps"
data="android.intent.category.LAUNCHER"
arg="--pct-syskeys" mCurArgData="null" mNextArg=5 argwas="--pct-syskeys" nextarg="0"
data="0"
  
[2025-06-05 13:32:18.5655][ INFO] 已启动阴阳师应用  
[2025-06-05 13:32:19.6256][ INFO] 图库初始化中. 图库源：本地，图库版本：1.4.19  
[2025-06-05 13:32:19.6256][DEBUG] 用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：1.4.19  
[2025-06-05 13:32:19.6502][DEBUG] Main_Pics 被更新！版本号：1.4.19  
[2025-06-05 13:32:19.6502][ INFO] 图库初始化完成，图库版本：1.4.19 载入状态：False 图库数量：673 字库数量：25  
[2025-06-05 13:32:19.6502][ INFO] 初始化大漠构建器成功  
[2025-06-05 13:32:23.1996][DEBUG] 插件注册免注册完成，版本号：7.2450  
[2025-06-05 13:32:23.8804][DEBUG] 插件收费注册完成，返回值：1  
[2025-06-05 13:32:23.8812][DEBUG] 插件版本：7.2450  
[2025-06-05 13:32:23.8812][DEBUG]  [定时任务(dcc38)] 任务线程已经在后台执行，UI控制权转移给用户！  
[2025-06-05 13:32:23.8812][ INFO] 任务'契灵50'启动成功  
[2025-06-05 13:32:23.8812][DEBUG]  [定时任务(dcc38)] 后台线程开启！  
[2025-06-05 13:32:23.8812][ INFO] 开始监控任务 [大号_dcc38] 执行超时，最长执行时间：3600秒  
[2025-06-05 13:32:23.8812][ INFO]  [定时任务(dcc38)] 欢迎使用蛋定助手，遇到异常、停止请蛋定，仔细查看帮助链接，请确保您的模拟器设置符合要求！
若有意料之外的错误，您可以加群(621016172)咨询管理员，谢谢！
5S后将继续执行，再说一遍：请确保您模拟器的设置符合我们的要求(分辨率、渲染引擎···)！  
[2025-06-05 13:32:28.8887][ INFO] 点击位置更新完成，更新模式: 胜利/失败[关闭], 达摩[关闭]  
[2025-06-05 13:32:28.8887][ INFO] 定时任务(dcc38) 当前执行: 绑定游戏  
[2025-06-05 13:32:28.8887][ INFO]  [定时任务(dcc38)] 句柄[5507790],开始执行脚本流程！  
[2025-06-05 13:32:28.8887][ INFO]  [定时任务(dcc38)] 主要对象开始绑定[主对象]  
[2025-06-05 13:32:28.8967][DEBUG]  [定时任务(dcc38)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 13:32:31.6211][ INFO]  [定时任务(dcc38)] 辅助对象开始绑定[检测对象]  
[2025-06-05 13:32:31.6211][DEBUG]  [定时任务(dcc38)] 尝试使用绑定模式 0 绑定窗口  
[2025-06-05 13:32:32.7176][DEBUG]  [定时任务(dcc38)] dm_id已捕获：main[389665500] sub[389683724]  
[2025-06-05 13:32:33.8906][ INFO]  [定时任务(dcc38)] 当前系统CPU：Intel Intel(R) Core(TM) i5-14600KF，显卡信息：NVIDIA GeForce RTX 4060 Ti，DPI是否为100%：1，系统OS版本：10.0.26100.3912 (WinBuild.160101.0800)  
[2025-06-05 13:32:35.8988][DEBUG]  [定时任务(dcc38)] 反检测配置 - 基础触发概率: 0.10%, 初始增量: 0.0100%, 增量倍率: 1.50, 延迟范围: 5-21秒, 调试模式: 关闭  
[2025-06-05 13:32:35.8988][DEBUG]  [定时任务(dcc38)] 小纸人配置 - 基础触发概率: 0.00%  
[2025-06-05 13:32:36.8908][ INFO]  [定时任务(dcc38)] 等待游戏到庭院后再启动主任务！  
[2025-06-05 13:32:36.9319][DEBUG]  [定时任务(dcc38)] 悬赏接受状态：False 御魂满后结束任务：False 卡屏后操作：重启游戏  
[2025-06-05 13:32:38.0326][ INFO]  [定时任务(dcc38)] 开始将游戏场景操作至庭院！  
[2025-06-05 13:32:40.4485][ INFO]  [定时任务(dcc38)] 当前默认的服务器为：价 夜摩天殿(非准确)，点击进入游戏..  
[2025-06-05 13:32:42.2782][ INFO]  [定时任务(dcc38)] 请确保您的卷轴皮肤为默认皮肤！..  
[2025-06-05 13:32:43.9003][ INFO]  [定时任务(dcc38)] 关闭牛皮癣广告弹窗..  
[2025-06-05 13:32:47.5135][ INFO]  [定时任务(dcc38)] 关闭牛皮癣广告弹窗..  
[2025-06-05 13:32:50.7232][ INFO]  [定时任务(dcc38)] 重启恢复游戏场景完成，当前界面为庭院，为您继续完成任务！  
[2025-06-05 13:32:50.8966][ INFO]  [定时任务(dcc38)] 开始分析依次执行任务清单！列表整体循环次数：1  
[2025-06-05 13:32:50.9021][DEBUG]  [定时任务(dcc38)] 设置的庭院皮肤为：枫色秋庭  
[2025-06-05 13:32:50.9021][ INFO] 定时任务(dcc38) 当前执行: 初始定时任务  
[2025-06-05 13:32:50.9021][ INFO]  [定时任务(dcc38)] 开始执行第1次任务循环！  
[2025-06-05 13:32:50.9021][ INFO] 定时任务(dcc38) 当前执行: 契灵  
[2025-06-05 13:32:50.9123][ INFO]  [定时任务(dcc38)] 开始执行任务：契灵 50次  
[2025-06-05 13:32:52.0123][ INFO]  [定时任务(dcc38)] 任务：[契灵] 重启游戏等事件监听已开启...  
[2025-06-05 13:32:53.1260][ WARN]  [定时任务(dcc38)] 开始全自动契灵任务，请注意以上几点：  
[2025-06-05 13:32:53.1329][ WARN]  [定时任务(dcc38)] 1.镇墓兽模式：请开启自动结契；  
[2025-06-05 13:32:53.1329][ WARN]  [定时任务(dcc38)] 2.预设更换操作只能在契灵之境中完成；  
[2025-06-05 13:32:55.4000][DEBUG] 场景识别成功：庭院  
[2025-06-05 13:32:55.4993][DEBUG] 场景识别成功：庭院  
[2025-06-05 13:32:55.4993][DEBUG]  [定时任务(dcc38)] [TanSuo] 当前场景:庭院  
[2025-06-05 13:32:55.5973][DEBUG] 场景识别成功：庭院  
[2025-06-05 13:32:55.6893][DEBUG] 场景识别成功：庭院  
[2025-06-05 13:32:55.6893][ INFO]  [定时任务(dcc38)] 首次启动，庭院尚未初始化，进入图鉴初始化人物位置..  
[2025-06-05 13:33:03.9186][ INFO]  [定时任务(dcc38)] 滑动一下屏幕.  
[2025-06-05 13:33:06.9621][ INFO]  [定时任务(dcc38)] 点击进入探索.  
[2025-06-05 13:33:11.2892][DEBUG] 场景识别成功：探索  
[2025-06-05 13:33:11.3524][DEBUG] Tesseract识别结果：11/30  
[2025-06-05 13:33:11.3524][DEBUG]  [定时任务(dcc38)] 本地Ocr识别突破卷结果：11  
[2025-06-05 13:33:11.4491][DEBUG] 场景识别成功：探索  
[2025-06-05 13:33:11.4491][DEBUG]  [定时任务(dcc38)] [QiLing] 当前场景:探索  
[2025-06-05 13:33:11.5470][DEBUG] 场景识别成功：探索  
[2025-06-05 13:33:11.6396][DEBUG] 场景识别成功：探索  
[2025-06-05 13:33:11.6396][DEBUG]  [定时任务(dcc38)] [TanSuo] 当前场景:探索  
[2025-06-05 13:33:11.7419][DEBUG] 场景识别成功：探索  
[2025-06-05 13:33:11.7419][ INFO]  [定时任务(dcc38)] 点击进入探索-契灵.  
[2025-06-05 13:33:15.6971][DEBUG] 场景识别成功：契灵  
[2025-06-05 13:33:15.6971][ INFO]  [定时任务(dcc38)] 当前战斗次数：0/50  
[2025-06-05 13:33:15.6971][ INFO]  [定时任务(dcc38)] 发现镇墓兽，点击镇墓兽位置  
[2025-06-05 13:33:17.3081][ INFO]  [定时任务(dcc38)] 进入镇墓兽_结契场景  
[2025-06-05 13:33:17.3081][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 13:33:20.1054][ INFO]  [定时任务(dcc38)] 关闭千咒式盘上限的提示  
[2025-06-05 13:33:21.5691][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:33:23.8877][ INFO] 任务 [大号_dcc38] 已运行 60 秒，超时阈值：3600 秒  
[2025-06-05 13:34:00.3063][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:34:00.6417][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:34:01.9137][DEBUG] Ai预测模型加载完成！[Classify]  
[2025-06-05 13:34:01.9864][DEBUG] YoloClassfly：未知  
[2025-06-05 13:34:01.9864][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:34:01.9864][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:34:03.1425][ INFO]  [定时任务(dcc38)] 当前胜利次数：1，当前捕捉成功次数：1  
[2025-06-05 13:34:03.1425][ INFO]  [定时任务(dcc38)] 当前战斗次数：1/50  
[2025-06-05 13:34:03.1638][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:34:03.1638][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 13:34:05.7911][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:34:23.8913][ INFO] 任务 [大号_dcc38] 已运行 120 秒，超时阈值：3600 秒  
[2025-06-05 13:34:33.8382][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:34:44.5227][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:34:45.0979][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:34:46.2409][DEBUG] YoloClassfly：tansuotask  
[2025-06-05 13:34:46.2411][DEBUG] AI场景识别成功：探索任务  
[2025-06-05 13:34:46.2411][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:34:47.5471][ INFO]  [定时任务(dcc38)] 当前胜利次数：2，当前捕捉成功次数：2  
[2025-06-05 13:34:47.5471][ INFO]  [定时任务(dcc38)] 当前战斗次数：2/50  
[2025-06-05 13:34:47.5576][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:34:47.5576][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 13:34:50.1486][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:35:23.8941][ INFO] 任务 [大号_dcc38] 已运行 180 秒，超时阈值：3600 秒  
[2025-06-05 13:35:27.7892][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:35:28.3070][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:35:29.3711][ INFO]  [定时任务(dcc38)] 当前胜利次数：3，当前捕捉成功次数：3  
[2025-06-05 13:35:29.3711][ INFO]  [定时任务(dcc38)] 当前战斗次数：3/50  
[2025-06-05 13:35:29.3859][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:35:29.3859][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 13:35:31.9852][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:36:10.9185][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:36:11.4477][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:36:12.6179][DEBUG] YoloClassfly：未知  
[2025-06-05 13:36:12.6179][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:36:12.6179][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:36:13.8353][ INFO]  [定时任务(dcc38)] 当前胜利次数：4，当前捕捉成功次数：4  
[2025-06-05 13:36:13.8353][ INFO]  [定时任务(dcc38)] 当前战斗次数：4/50  
[2025-06-05 13:36:13.8507][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:36:13.8507][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 13:36:16.5389][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:36:23.8949][ INFO] 任务 [大号_dcc38] 已运行 240 秒，超时阈值：3600 秒  
[2025-06-05 13:36:55.4397][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:36:55.9420][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:36:57.0311][ INFO]  [定时任务(dcc38)] 当前胜利次数：5，当前捕捉成功次数：5  
[2025-06-05 13:36:57.0311][ INFO]  [定时任务(dcc38)] 当前战斗次数：5/50  
[2025-06-05 13:36:57.0446][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:36:57.0446][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 13:36:59.6265][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:37:23.8969][ INFO] 任务 [大号_dcc38] 已运行 300 秒，超时阈值：3600 秒  
[2025-06-05 13:37:27.5382][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:37:28.0142][DEBUG] CheckUserStatus|Running...  
[2025-06-05 13:37:28.2377][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 13:37:34.1019][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:37:34.6924][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:37:35.8389][DEBUG] YoloClassfly：未知  
[2025-06-05 13:37:35.8389][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:37:37.0675][DEBUG] YoloClassfly：未知  
[2025-06-05 13:37:37.0675][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:37:38.2841][DEBUG] YoloClassfly：未知  
[2025-06-05 13:37:38.2841][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:37:39.4421][DEBUG] YoloClassfly：未知  
[2025-06-05 13:37:39.4421][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:37:39.4475][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:37:40.7126][DEBUG] YoloClassfly：未知  
[2025-06-05 13:37:40.7126][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:37:40.7126][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:37:41.9487][ INFO]  [定时任务(dcc38)] 当前胜利次数：6，当前捕捉成功次数：6  
[2025-06-05 13:37:41.9487][ INFO]  [定时任务(dcc38)] 当前战斗次数：6/50  
[2025-06-05 13:37:41.9663][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:37:41.9663][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 13:37:44.6321][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:38:23.3190][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:38:23.8570][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:38:23.9007][ INFO] 任务 [大号_dcc38] 已运行 360 秒，超时阈值：3600 秒  
[2025-06-05 13:38:24.9748][DEBUG] YoloClassfly：未知  
[2025-06-05 13:38:24.9748][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:38:24.9787][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:38:26.1675][ INFO]  [定时任务(dcc38)] 当前胜利次数：7，当前捕捉成功次数：7  
[2025-06-05 13:38:26.1675][ INFO]  [定时任务(dcc38)] 当前战斗次数：7/50  
[2025-06-05 13:38:26.1675][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:38:26.1675][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 13:38:28.7490][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:39:06.5629][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:39:07.1247][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:39:08.1735][ INFO]  [定时任务(dcc38)] 当前胜利次数：8，当前捕捉成功次数：8  
[2025-06-05 13:39:08.1735][ INFO]  [定时任务(dcc38)] 当前战斗次数：8/50  
[2025-06-05 13:39:08.1879][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:39:08.1879][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%  
[2025-06-05 13:39:10.7676][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:39:23.9021][ INFO] 任务 [大号_dcc38] 已运行 420 秒，超时阈值：3600 秒  
[2025-06-05 13:39:37.6519][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_失败2  
[2025-06-05 13:39:39.3221][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:39:40.5229][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:40.5229][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:41.6771][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:41.6771][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:42.8482][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:42.8482][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:44.0042][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:44.0042][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:45.2066][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:45.2066][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:46.3641][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:46.3641][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:47.4923][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:47.4923][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:48.6811][DEBUG] YoloClassfly：未知  
[2025-06-05 13:39:48.6811][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:39:48.6811][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:39:50.1247][ INFO]  [定时任务(dcc38)] 当前胜利次数：9，当前捕捉成功次数：8  
[2025-06-05 13:39:50.1247][ INFO]  [定时任务(dcc38)] 当前战斗次数：9/50  
[2025-06-05 13:39:50.1354][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:39:50.1354][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%  
[2025-06-05 13:39:52.7765][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:40:18.6733][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:40:23.9057][ INFO] 任务 [大号_dcc38] 已运行 480 秒，超时阈值：3600 秒  
[2025-06-05 13:40:30.3837][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:40:30.9862][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:40:32.0555][ INFO]  [定时任务(dcc38)] 当前胜利次数：10，当前捕捉成功次数：9  
[2025-06-05 13:40:32.0555][ INFO]  [定时任务(dcc38)] 当前战斗次数：10/50  
[2025-06-05 13:40:32.0555][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:40:32.0555][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%  
[2025-06-05 13:40:34.8351][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:41:13.7483][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:41:14.3810][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:41:15.4269][ INFO]  [定时任务(dcc38)] 当前胜利次数：11，当前捕捉成功次数：10  
[2025-06-05 13:41:15.4269][ INFO]  [定时任务(dcc38)] 当前战斗次数：11/50  
[2025-06-05 13:41:15.4431][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:41:15.4466][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%  
[2025-06-05 13:41:18.0883][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:41:23.9087][ INFO] 任务 [大号_dcc38] 已运行 540 秒，超时阈值：3600 秒  
[2025-06-05 13:41:55.6237][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:41:56.2060][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:41:57.2963][ INFO]  [定时任务(dcc38)] 当前胜利次数：12，当前捕捉成功次数：11  
[2025-06-05 13:41:57.3136][ INFO]  [定时任务(dcc38)] 当前战斗次数：12/50  
[2025-06-05 13:41:57.3239][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:41:57.3239][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%  
[2025-06-05 13:42:00.1137][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:42:23.9110][ INFO] 任务 [大号_dcc38] 已运行 600 秒，超时阈值：3600 秒  
[2025-06-05 13:42:33.3360][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:42:33.9169][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:42:35.0869][DEBUG] YoloClassfly：未知  
[2025-06-05 13:42:35.0872][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:42:36.2855][DEBUG] YoloClassfly：未知  
[2025-06-05 13:42:36.2855][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:42:37.5065][DEBUG] YoloClassfly：未知  
[2025-06-05 13:42:37.5065][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:42:38.6600][DEBUG] YoloClassfly：未知  
[2025-06-05 13:42:38.6600][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:42:38.6600][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:42:39.9786][DEBUG] YoloClassfly：未知  
[2025-06-05 13:42:39.9786][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:42:39.9786][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:42:41.0944][ INFO]  [定时任务(dcc38)] 当前胜利次数：13，当前捕捉成功次数：12  
[2025-06-05 13:42:41.1102][ INFO]  [定时任务(dcc38)] 当前战斗次数：13/50  
[2025-06-05 13:42:41.1350][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:42:41.1350][ INFO]  [定时任务(dcc38)] 触发随机延迟等待，当前触发概率：3.97%，等待时长：23秒  
[2025-06-05 13:43:06.9151][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:43:23.9118][ INFO] 任务 [大号_dcc38] 已运行 660 秒，超时阈值：3600 秒  
[2025-06-05 13:43:34.4777][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:43:46.2660][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:43:46.8095][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:43:47.9281][ INFO]  [定时任务(dcc38)] 当前胜利次数：14，当前捕捉成功次数：13  
[2025-06-05 13:43:47.9281][ INFO]  [定时任务(dcc38)] 当前战斗次数：14/50  
[2025-06-05 13:43:47.9425][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:43:47.9425][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 13:43:50.6913][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:44:23.9147][ INFO] 任务 [大号_dcc38] 已运行 720 秒，超时阈值：3600 秒  
[2025-06-05 13:44:28.2226][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:44:28.8249][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:44:29.9440][ INFO]  [定时任务(dcc38)] 当前胜利次数：15，当前捕捉成功次数：14  
[2025-06-05 13:44:29.9440][ INFO]  [定时任务(dcc38)] 当前战斗次数：15/50  
[2025-06-05 13:44:29.9631][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:44:29.9631][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 13:44:32.6221][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:45:00.4305][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:45:12.2472][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:45:12.7864][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:45:13.9052][ INFO]  [定时任务(dcc38)] 当前胜利次数：16，当前捕捉成功次数：15  
[2025-06-05 13:45:13.9052][ INFO]  [定时任务(dcc38)] 当前战斗次数：16/50  
[2025-06-05 13:45:13.9269][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:45:13.9269][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 13:45:16.5001][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:45:23.9185][ INFO] 任务 [大号_dcc38] 已运行 780 秒，超时阈值：3600 秒  
[2025-06-05 13:45:49.6955][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:45:50.2806][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:45:51.4322][DEBUG] YoloClassfly：未知  
[2025-06-05 13:45:51.4322][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:45:52.5963][DEBUG] YoloClassfly：未知  
[2025-06-05 13:45:52.5963][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:45:53.7970][DEBUG] YoloClassfly：未知  
[2025-06-05 13:45:53.7970][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:45:54.9676][DEBUG] YoloClassfly：未知  
[2025-06-05 13:45:54.9676][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:45:54.9676][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:45:56.2853][DEBUG] YoloClassfly：未知  
[2025-06-05 13:45:56.2853][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:45:56.2853][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:45:57.4901][ INFO]  [定时任务(dcc38)] 当前胜利次数：17，当前捕捉成功次数：16  
[2025-06-05 13:45:57.4901][ INFO]  [定时任务(dcc38)] 当前战斗次数：17/50  
[2025-06-05 13:45:57.5203][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:45:57.5203][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 13:46:00.2426][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:46:23.9244][ INFO] 任务 [大号_dcc38] 已运行 840 秒，超时阈值：3600 秒  
[2025-06-05 13:46:25.9207][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:46:32.2339][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_失败2  
[2025-06-05 13:46:34.0272][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:46:35.1803][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:35.1833][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:35.1833][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:46:36.5824][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:36.5824][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:37.7616][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:37.7616][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:38.8953][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:38.8953][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:40.1101][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:40.1101][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:40.1101][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:46:41.4815][DEBUG] YoloClassfly：未知  
[2025-06-05 13:46:41.4815][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:46:41.4815][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:46:42.6964][ INFO]  [定时任务(dcc38)] 当前胜利次数：18，当前捕捉成功次数：16  
[2025-06-05 13:46:42.6964][ INFO]  [定时任务(dcc38)] 当前战斗次数：18/50  
[2025-06-05 13:46:42.7333][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:46:42.7490][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 13:46:45.5441][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:47:11.3115][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:47:18.8403][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:47:19.4852][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:47:20.6066][DEBUG] YoloClassfly：未知  
[2025-06-05 13:47:20.6066][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:47:21.7504][DEBUG] YoloClassfly：未知  
[2025-06-05 13:47:21.7504][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:47:22.9444][DEBUG] YoloClassfly：tansuo  
[2025-06-05 13:47:22.9444][DEBUG] AI场景识别成功：探索  
[2025-06-05 13:47:23.9254][ INFO] 任务 [大号_dcc38] 已运行 900 秒，超时阈值：3600 秒  
[2025-06-05 13:47:24.1096][DEBUG] YoloClassfly：未知  
[2025-06-05 13:47:24.1096][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:47:25.2750][DEBUG] YoloClassfly：未知  
[2025-06-05 13:47:25.2750][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:47:25.2885][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:47:26.5227][ INFO]  [定时任务(dcc38)] 当前胜利次数：19，当前捕捉成功次数：17  
[2025-06-05 13:47:26.5227][ INFO]  [定时任务(dcc38)] 当前战斗次数：19/50  
[2025-06-05 13:47:26.5519][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:47:26.5519][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 13:47:29.2008][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:47:55.8143][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:48:07.6488][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:48:08.1804][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:48:09.2873][ INFO]  [定时任务(dcc38)] 当前胜利次数：20，当前捕捉成功次数：18  
[2025-06-05 13:48:09.3026][ INFO]  [定时任务(dcc38)] 当前战斗次数：20/50  
[2025-06-05 13:48:09.3161][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:48:09.3407][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 13:48:11.9034][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:48:23.9288][ INFO] 任务 [大号_dcc38] 已运行 960 秒，超时阈值：3600 秒  
[2025-06-05 13:48:37.9150][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:48:45.4640][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:48:46.1239][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:48:47.2787][DEBUG] YoloClassfly：未知  
[2025-06-05 13:48:47.2787][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:48:48.4592][DEBUG] YoloClassfly：未知  
[2025-06-05 13:48:48.4592][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:48:49.6731][DEBUG] YoloClassfly：未知  
[2025-06-05 13:48:49.6731][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:48:50.8113][DEBUG] YoloClassfly：未知  
[2025-06-05 13:48:50.8113][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:48:51.9855][DEBUG] YoloClassfly：未知  
[2025-06-05 13:48:51.9855][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:48:51.9855][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:48:53.1560][ INFO]  [定时任务(dcc38)] 当前胜利次数：21，当前捕捉成功次数：19  
[2025-06-05 13:48:53.1763][ INFO]  [定时任务(dcc38)] 当前战斗次数：21/50  
[2025-06-05 13:48:53.1929][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:48:53.1929][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 13:48:55.9286][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:49:21.8206][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:49:23.9328][ INFO] 任务 [大号_dcc38] 已运行 1020 秒，超时阈值：3600 秒  
[2025-06-05 13:49:29.3298][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:49:29.9382][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:49:31.1004][DEBUG] YoloClassfly：未知  
[2025-06-05 13:49:31.1004][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:49:32.2998][DEBUG] YoloClassfly：未知  
[2025-06-05 13:49:32.2998][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:49:33.5228][DEBUG] YoloClassfly：tansuo  
[2025-06-05 13:49:33.5228][DEBUG] AI场景识别成功：探索  
[2025-06-05 13:49:34.6573][DEBUG] YoloClassfly：未知  
[2025-06-05 13:49:34.6573][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:49:35.8137][DEBUG] YoloClassfly：未知  
[2025-06-05 13:49:35.8137][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:49:35.8137][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:49:36.9843][ INFO]  [定时任务(dcc38)] 当前胜利次数：22，当前捕捉成功次数：20  
[2025-06-05 13:49:36.9843][ INFO]  [定时任务(dcc38)] 当前战斗次数：22/50  
[2025-06-05 13:49:37.0129][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:49:37.0129][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%  
[2025-06-05 13:49:39.7679][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:50:07.5443][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:50:14.0683][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:50:14.7534][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:50:15.9135][DEBUG] YoloClassfly：未知  
[2025-06-05 13:50:15.9135][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:50:17.0999][DEBUG] YoloClassfly：未知  
[2025-06-05 13:50:17.0999][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:50:18.2876][DEBUG] YoloClassfly：未知  
[2025-06-05 13:50:18.2876][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:50:19.4545][DEBUG] YoloClassfly：未知  
[2025-06-05 13:50:19.4545][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:50:19.4545][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:50:20.7468][DEBUG] YoloClassfly：未知  
[2025-06-05 13:50:20.7468][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:50:20.7468][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:50:21.9811][ INFO]  [定时任务(dcc38)] 当前胜利次数：23，当前捕捉成功次数：21  
[2025-06-05 13:50:21.9811][ INFO]  [定时任务(dcc38)] 当前战斗次数：23/50  
[2025-06-05 13:50:22.0032][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:50:22.0195][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%  
[2025-06-05 13:50:23.9376][ INFO] 任务 [大号_dcc38] 已运行 1080 秒，超时阈值：3600 秒  
[2025-06-05 13:50:24.7137][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:51:02.2817][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:51:02.9193][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:51:04.0096][ INFO]  [定时任务(dcc38)] 当前胜利次数：24，当前捕捉成功次数：22  
[2025-06-05 13:51:04.0483][ INFO]  [定时任务(dcc38)] 当前战斗次数：24/50  
[2025-06-05 13:51:04.0761][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:51:04.0761][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%  
[2025-06-05 13:51:06.6431][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:51:23.9405][ INFO] 任务 [大号_dcc38] 已运行 1140 秒，超时阈值：3600 秒  
[2025-06-05 13:51:44.3714][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:51:44.9949][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:51:46.1122][ INFO]  [定时任务(dcc38)] 当前胜利次数：25，当前捕捉成功次数：23  
[2025-06-05 13:51:46.1327][ INFO]  [定时任务(dcc38)] 当前战斗次数：25/50  
[2025-06-05 13:51:46.1499][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:51:46.1499][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%  
[2025-06-05 13:51:48.7022][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:52:23.9452][ INFO] 任务 [大号_dcc38] 已运行 1200 秒，超时阈值：3600 秒  
[2025-06-05 13:52:26.3617][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:52:26.9519][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:52:27.9983][ INFO]  [定时任务(dcc38)] 当前胜利次数：26，当前捕捉成功次数：24  
[2025-06-05 13:52:28.0273][ INFO]  [定时任务(dcc38)] 当前战斗次数：26/50  
[2025-06-05 13:52:28.0490][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:52:28.0490][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%  
[2025-06-05 13:52:30.6327][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:52:57.3617][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:53:08.1156][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:53:08.6798][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:53:09.8455][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:09.8455][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:09.8455][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:53:11.0455][ INFO]  [定时任务(dcc38)] 当前胜利次数：27，当前捕捉成功次数：25  
[2025-06-05 13:53:11.0455][ INFO]  [定时任务(dcc38)] 当前战斗次数：27/50  
[2025-06-05 13:53:11.0910][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:53:11.0910][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%  
[2025-06-05 13:53:13.7784][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:53:23.9490][ INFO] 任务 [大号_dcc38] 已运行 1260 秒，超时阈值：3600 秒  
[2025-06-05 13:53:41.8216][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_失败2  
[2025-06-05 13:53:43.4804][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:53:44.6006][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:44.6006][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:45.7816][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:45.7816][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:46.9665][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:46.9665][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:48.1966][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:48.1966][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:49.4055][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:49.4055][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:50.6257][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:50.6257][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:51.8273][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:51.8273][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:52.9830][DEBUG] YoloClassfly：未知  
[2025-06-05 13:53:52.9830][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:53:52.9830][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:53:54.3459][ INFO]  [定时任务(dcc38)] 当前胜利次数：28，当前捕捉成功次数：25  
[2025-06-05 13:53:54.3735][ INFO]  [定时任务(dcc38)] 当前战斗次数：28/50  
[2025-06-05 13:53:54.3911][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:53:54.3911][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%  
[2025-06-05 13:53:57.1499][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:54:23.9540][ INFO] 任务 [大号_dcc38] 已运行 1320 秒，超时阈值：3600 秒  
[2025-06-05 13:54:36.8454][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:54:37.3479][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:54:38.4662][ INFO]  [定时任务(dcc38)] 当前胜利次数：29，当前捕捉成功次数：26  
[2025-06-05 13:54:38.4668][ INFO]  [定时任务(dcc38)] 当前战斗次数：29/50  
[2025-06-05 13:54:38.4927][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:54:38.5135][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：13.22%，下次增量：6.5684%  
[2025-06-05 13:54:41.1966][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:55:06.9538][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:55:18.7032][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:55:19.2884][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:55:20.3350][ INFO]  [定时任务(dcc38)] 当前胜利次数：30，当前捕捉成功次数：27  
[2025-06-05 13:55:20.3350][ INFO]  [定时任务(dcc38)] 当前战斗次数：30/50  
[2025-06-05 13:55:20.3547][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:55:20.3732][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：19.79%，下次增量：9.8526%  
[2025-06-05 13:55:22.9003][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:55:23.9552][ INFO] 任务 [大号_dcc38] 已运行 1380 秒，超时阈值：3600 秒  
[2025-06-05 13:55:50.7900][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:55:58.5264][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 13:55:59.0362][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:56:00.1839][DEBUG] YoloClassfly：未知  
[2025-06-05 13:56:00.1839][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:56:01.3169][DEBUG] YoloClassfly：未知  
[2025-06-05 13:56:01.3169][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:56:02.4344][DEBUG] YoloClassfly：tansuo  
[2025-06-05 13:56:02.4344][DEBUG] AI场景识别成功：探索  
[2025-06-05 13:56:03.6015][DEBUG] YoloClassfly：未知  
[2025-06-05 13:56:03.6015][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:56:04.7538][DEBUG] YoloClassfly：未知  
[2025-06-05 13:56:04.7538][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:56:04.7538][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:56:06.0007][ INFO]  [定时任务(dcc38)] 当前胜利次数：31，当前捕捉成功次数：28  
[2025-06-05 13:56:06.0297][ INFO]  [定时任务(dcc38)] 当前战斗次数：31/50  
[2025-06-05 13:56:06.0540][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:56:06.0540][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：29.64%，下次增量：14.7789%  
[2025-06-05 13:56:08.7601][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:56:23.9600][ INFO] 任务 [大号_dcc38] 已运行 1440 秒，超时阈值：3600 秒  
[2025-06-05 13:56:34.6953][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:56:46.6678][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:56:47.1755][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:56:48.2088][ INFO]  [定时任务(dcc38)] 当前胜利次数：32，当前捕捉成功次数：29  
[2025-06-05 13:56:48.2088][ INFO]  [定时任务(dcc38)] 当前战斗次数：32/50  
[2025-06-05 13:56:48.2408][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:56:48.2408][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：44.42%，下次增量：22.1684%  
[2025-06-05 13:56:50.8698][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:57:17.6107][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 13:57:23.9642][ INFO] 任务 [大号_dcc38] 已运行 1500 秒，超时阈值：3600 秒  
[2025-06-05 13:57:29.3504][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:57:29.8566][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:57:30.9735][ INFO]  [定时任务(dcc38)] 当前胜利次数：33，当前捕捉成功次数：30  
[2025-06-05 13:57:30.9735][ INFO]  [定时任务(dcc38)] 当前战斗次数：33/50  
[2025-06-05 13:57:30.9957][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:57:30.9957][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：66.59%，下次增量：33.2526%  
[2025-06-05 13:57:33.5725][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:58:11.2500][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:58:11.8257][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:58:12.8524][ INFO]  [定时任务(dcc38)] 当前胜利次数：34，当前捕捉成功次数：31  
[2025-06-05 13:58:12.8864][ INFO]  [定时任务(dcc38)] 当前战斗次数：34/50  
[2025-06-05 13:58:12.9224][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:58:12.9224][ INFO]  [定时任务(dcc38)] 触发随机延迟等待，当前触发概率：66.59%，等待时长：40秒  
[2025-06-05 13:58:23.9666][ INFO] 任务 [大号_dcc38] 已运行 1560 秒，超时阈值：3600 秒  
[2025-06-05 13:58:55.6587][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 13:59:23.9700][ INFO] 任务 [大号_dcc38] 已运行 1620 秒，超时阈值：3600 秒  
[2025-06-05 13:59:34.1071][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 13:59:34.6907][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 13:59:35.8725][DEBUG] YoloClassfly：未知  
[2025-06-05 13:59:35.8725][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 13:59:35.8773][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 13:59:37.0568][ INFO]  [定时任务(dcc38)] 当前胜利次数：35，当前捕捉成功次数：32  
[2025-06-05 13:59:37.0908][ INFO]  [定时任务(dcc38)] 当前战斗次数：35/50  
[2025-06-05 13:59:37.1153][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 13:59:37.1153][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.11%，下次增量：0.0150%  
[2025-06-05 13:59:39.7301][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:00:17.4715][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:00:17.9730][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:00:19.0228][ INFO]  [定时任务(dcc38)] 当前胜利次数：36，当前捕捉成功次数：33  
[2025-06-05 14:00:19.0490][ INFO]  [定时任务(dcc38)] 当前战斗次数：36/50  
[2025-06-05 14:00:19.0703][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:00:19.0703][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.12%，下次增量：0.0225%  
[2025-06-05 14:00:21.6697][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:00:23.9729][ INFO] 任务 [大号_dcc38] 已运行 1680 秒，超时阈值：3600 秒  
[2025-06-05 14:00:59.1417][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:00:59.6599][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:01:00.8592][DEBUG] YoloClassfly：未知  
[2025-06-05 14:01:00.8592][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:01:00.8592][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:01:02.0348][ INFO]  [定时任务(dcc38)] 当前胜利次数：37，当前捕捉成功次数：34  
[2025-06-05 14:01:02.0724][ INFO]  [定时任务(dcc38)] 当前战斗次数：37/50  
[2025-06-05 14:01:02.0945][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:01:02.0945][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.15%，下次增量：0.0337%  
[2025-06-05 14:01:04.6964][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:01:23.9762][ INFO] 任务 [大号_dcc38] 已运行 1740 秒，超时阈值：3600 秒  
[2025-06-05 14:01:32.5430][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:01:44.2856][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:01:44.8182][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:01:45.9241][ INFO]  [定时任务(dcc38)] 当前胜利次数：38，当前捕捉成功次数：35  
[2025-06-05 14:01:45.9479][ INFO]  [定时任务(dcc38)] 当前战斗次数：38/50  
[2025-06-05 14:01:45.9695][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:01:45.9695][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.18%，下次增量：0.0506%  
[2025-06-05 14:01:48.5534][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:02:16.3682][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:02:23.9826][ INFO] 任务 [大号_dcc38] 已运行 1800 秒，超时阈值：3600 秒  
[2025-06-05 14:02:27.2054][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 14:02:27.8262][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:02:28.9677][DEBUG] YoloClassfly：未知  
[2025-06-05 14:02:28.9677][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:02:28.9732][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:02:30.1844][ INFO]  [定时任务(dcc38)] 当前胜利次数：39，当前捕捉成功次数：36  
[2025-06-05 14:02:30.2205][ INFO]  [定时任务(dcc38)] 当前战斗次数：39/50  
[2025-06-05 14:02:30.2422][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:02:30.2422][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.23%，下次增量：0.0759%  
[2025-06-05 14:02:32.7991][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:02:59.3883][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:03:10.3331][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:03:10.9378][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:03:12.0971][DEBUG] YoloClassfly：未知  
[2025-06-05 14:03:12.0971][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:03:13.2087][ INFO]  [定时任务(dcc38)] 当前胜利次数：40，当前捕捉成功次数：37  
[2025-06-05 14:03:13.2513][ INFO]  [定时任务(dcc38)] 当前战斗次数：40/50  
[2025-06-05 14:03:13.2777][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:03:13.2777][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.31%，下次增量：0.1139%  
[2025-06-05 14:03:15.8632][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:03:23.9890][ INFO] 任务 [大号_dcc38] 已运行 1860 秒，超时阈值：3600 秒  
[2025-06-05 14:03:42.6320][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:03:54.3463][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:03:54.8705][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:03:55.9691][ INFO]  [定时任务(dcc38)] 当前胜利次数：41，当前捕捉成功次数：38  
[2025-06-05 14:03:56.0182][ INFO]  [定时任务(dcc38)] 当前战斗次数：41/50  
[2025-06-05 14:03:56.0498][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:03:56.0793][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.42%，下次增量：0.1709%  
[2025-06-05 14:03:58.6923][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:04:23.9925][ INFO] 任务 [大号_dcc38] 已运行 1920 秒，超时阈值：3600 秒  
[2025-06-05 14:04:36.4765][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:04:36.9928][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:04:38.0898][ INFO]  [定时任务(dcc38)] 当前胜利次数：42，当前捕捉成功次数：39  
[2025-06-05 14:04:38.1424][ INFO]  [定时任务(dcc38)] 当前战斗次数：42/50  
[2025-06-05 14:04:38.1669][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:04:38.1669][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.59%，下次增量：0.2563%  
[2025-06-05 14:04:40.8603][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:05:06.7520][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:05:14.3428][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_结契成功1  
[2025-06-05 14:05:14.8602][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:05:16.0924][DEBUG] YoloClassfly：未知  
[2025-06-05 14:05:16.0924][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:05:17.2775][DEBUG] YoloClassfly：未知  
[2025-06-05 14:05:17.2775][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:05:18.4149][DEBUG] YoloClassfly：未知  
[2025-06-05 14:05:18.4149][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:05:19.6233][DEBUG] YoloClassfly：未知  
[2025-06-05 14:05:19.6233][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:05:19.6264][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩1  
[2025-06-05 14:05:21.0283][DEBUG] YoloClassfly：未知  
[2025-06-05 14:05:21.0283][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:05:21.0331][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:05:22.2506][ INFO]  [定时任务(dcc38)] 当前胜利次数：43，当前捕捉成功次数：40  
[2025-06-05 14:05:22.2824][ INFO]  [定时任务(dcc38)] 当前战斗次数：43/50  
[2025-06-05 14:05:22.3062][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:05:22.3062][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：0.85%，下次增量：0.3844%  
[2025-06-05 14:05:23.9932][ INFO] 任务 [大号_dcc38] 已运行 1980 秒，超时阈值：3600 秒  
[2025-06-05 14:05:24.9700][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:05:52.6191][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:06:04.4437][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:06:05.0727][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:06:06.2187][DEBUG] YoloClassfly：未知  
[2025-06-05 14:06:06.2187][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:06:07.2899][ INFO]  [定时任务(dcc38)] 当前胜利次数：44，当前捕捉成功次数：41  
[2025-06-05 14:06:07.2899][ INFO]  [定时任务(dcc38)] 当前战斗次数：44/50  
[2025-06-05 14:06:07.3202][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:06:07.3607][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.23%，下次增量：0.5767%  
[2025-06-05 14:06:09.9669][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:06:23.9953][ INFO] 任务 [大号_dcc38] 已运行 2040 秒，超时阈值：3600 秒  
[2025-06-05 14:06:40.1157][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_失败2  
[2025-06-05 14:06:41.9159][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:06:43.1057][DEBUG] YoloClassfly：未知  
[2025-06-05 14:06:43.1057][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:06:44.2323][DEBUG] YoloClassfly：未知  
[2025-06-05 14:06:44.2323][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:06:45.3499][DEBUG] YoloClassfly：未知  
[2025-06-05 14:06:45.3499][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:06:46.5548][DEBUG] YoloClassfly：tingzhong  
[2025-06-05 14:06:46.5548][DEBUG] AI场景识别成功：町中  
[2025-06-05 14:06:47.7512][DEBUG] YoloClassfly：未知  
[2025-06-05 14:06:47.7512][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:06:47.7552][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:06:49.1728][DEBUG] YoloClassfly：tansuotask  
[2025-06-05 14:06:49.1728][DEBUG] AI场景识别成功：探索任务  
[2025-06-05 14:06:49.1728][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:06:50.4496][ INFO]  [定时任务(dcc38)] 当前胜利次数：45，当前捕捉成功次数：41  
[2025-06-05 14:06:50.4860][ INFO]  [定时任务(dcc38)] 当前战斗次数：45/50  
[2025-06-05 14:06:50.5119][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:06:50.5484][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：1.81%，下次增量：0.8650%  
[2025-06-05 14:06:53.2560][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:07:19.9947][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_失败2  
[2025-06-05 14:07:21.7155][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:07:22.9259][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:22.9259][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:23.9990][ INFO] 任务 [大号_dcc38] 已运行 2100 秒，超时阈值：3600 秒  
[2025-06-05 14:07:24.0889][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:24.0889][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:25.2882][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:25.2882][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:26.4718][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:26.4718][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:27.6794][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:27.6794][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:28.8729][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:28.8729][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:30.0617][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:30.0617][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:31.2044][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:31.2044][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:31.2096][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:07:32.4881][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:32.4881][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:32.4881][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:07:33.7495][DEBUG] YoloClassfly：未知  
[2025-06-05 14:07:33.7495][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:07:34.7783][ INFO]  [定时任务(dcc38)] 当前胜利次数：46，当前捕捉成功次数：41  
[2025-06-05 14:07:34.8305][ INFO]  [定时任务(dcc38)] 当前战斗次数：46/50  
[2025-06-05 14:07:34.8666][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:07:34.9082][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：2.67%，下次增量：1.2975%  
[2025-06-05 14:07:37.5804][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:08:04.7690][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:08:16.5052][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:08:17.0811][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:08:18.1684][ INFO]  [定时任务(dcc38)] 当前胜利次数：47，当前捕捉成功次数：42  
[2025-06-05 14:08:18.2052][ INFO]  [定时任务(dcc38)] 当前战斗次数：47/50  
[2025-06-05 14:08:18.2322][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:08:18.2571][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：3.97%，下次增量：1.9462%  
[2025-06-05 14:08:20.8465][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:08:24.0037][ INFO] 任务 [大号_dcc38] 已运行 2160 秒，超时阈值：3600 秒  
[2025-06-05 14:08:48.7411][DEBUG]  [定时任务(dcc38)] 罗盘识别次数：1/5  
[2025-06-05 14:08:59.6976][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:09:00.1977][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:09:01.2482][ INFO]  [定时任务(dcc38)] 当前胜利次数：48，当前捕捉成功次数：43  
[2025-06-05 14:09:01.2847][ INFO]  [定时任务(dcc38)] 当前战斗次数：48/50  
[2025-06-05 14:09:01.3077][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:09:01.3342][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：5.92%，下次增量：2.9193%  
[2025-06-05 14:09:03.9620][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:09:24.0058][ INFO] 任务 [大号_dcc38] 已运行 2220 秒，超时阈值：3600 秒  
[2025-06-05 14:09:42.7072][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:09:43.2758][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:09:44.4086][DEBUG] YoloClassfly：未知  
[2025-06-05 14:09:44.4086][DEBUG] AI场景识别成功：未知[YOLO]  
[2025-06-05 14:09:44.4086][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:09:45.6338][ INFO]  [定时任务(dcc38)] 当前胜利次数：49，当前捕捉成功次数：44  
[2025-06-05 14:09:45.6753][ INFO]  [定时任务(dcc38)] 当前战斗次数：49/50  
[2025-06-05 14:09:45.7079][ INFO]  [定时任务(dcc38)] 在镇墓兽_结契场景中，开始战斗  
[2025-06-05 14:09:45.7079][DEBUG]  [定时任务(dcc38)] 未触发随机延迟，当前触发概率已提升至：8.84%，下次增量：4.3789%  
[2025-06-05 14:09:48.3618][ INFO]  [定时任务(dcc38)] 战斗开始...  
[2025-06-05 14:10:24.0084][ INFO] 任务 [大号_dcc38] 已运行 2280 秒，超时阈值：3600 秒  
[2025-06-05 14:10:26.0207][ INFO]  [定时任务(dcc38)] 执行点击：契灵.战斗_达摩  
[2025-06-05 14:10:26.5453][ INFO]  [定时任务(dcc38)] 战斗胜利(Combat_End)..  
[2025-06-05 14:10:27.6671][ INFO]  [定时任务(dcc38)] 当前胜利次数：50，当前捕捉成功次数：45  
[2025-06-05 14:10:27.7119][ INFO]  [定时任务(dcc38)] 战斗次数达到上限，退出  
[2025-06-05 14:10:27.7119][ INFO]  [定时任务(dcc38)] 契灵任务完成，退出到探索..  
[2025-06-05 14:10:33.9416][ INFO]  [定时任务(dcc38)] 脚本任务结束  
[2025-06-05 14:10:34.1014][DEBUG]  [定时任务(dcc38)] 任务:[Sub]被取消..  
[2025-06-05 14:10:35.1960][ INFO] 定时任务已完成，点击次数：147，执行任务：契灵-50次  
[2025-06-05 14:10:35.1960][ INFO] 清理任务 [大号_dcc38] 的执行信息和超时监控  
[2025-06-05 14:10:35.1960][ INFO] 任务执行时长：38.19分钟  
[2025-06-05 14:10:35.6578][ INFO] [App] 通知发送成功  
[2025-06-05 14:10:35.8095][ INFO] [微信推送] 通知发送成功  
[2025-06-05 14:10:35.8095][ INFO] 成功发送通知类型: App, 微信推送  
[2025-06-05 14:10:35.8095][ INFO] 任务结束通知发送成功！执行时长：38.19分钟  
[2025-06-05 14:10:35.8095][ INFO] 正在关闭模拟器...  
[2025-06-05 14:10:36.0597][DEBUG] 已发送关闭命令给模拟器，索引: 0  
[2025-06-05 14:10:36.0871][ INFO] 脚本任务 [大号_dcc38] 已完成，已从跟踪列表中移除  
[2025-06-05 14:10:36.0871][ INFO] 模拟器已关闭  
[2025-06-05 14:10:36.0871][DEBUG] 更新任务历史记录状态：b485f870-8808-4971-baf0-7129cfa03341，新状态：成功  
[2025-06-05 14:10:44.0186][ INFO] 任务 [大号_dcc38] 的执行信息已不存在，停止超时监控  
[2025-06-05 14:37:28.0386][DEBUG] CheckUserStatus|Running...  
[2025-06-05 14:37:28.2978][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 15:37:28.0527][DEBUG] CheckUserStatus|Running...  
[2025-06-05 15:37:28.2792][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 16:37:28.0795][DEBUG] CheckUserStatus|Running...  
[2025-06-05 16:37:28.3088][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 17:37:28.1030][DEBUG] CheckUserStatus|Running...  
[2025-06-05 17:37:28.3191][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 18:37:28.1232][DEBUG] CheckUserStatus|Running...  
[2025-06-05 18:37:28.3872][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 19:01:08.2997][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 19:01:08.2997][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 19:01:08.2997][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 19:01:08.3136][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:01:08.3136][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:01:08.3136][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:01:08.3136][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:01:08.3136][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 19:01:08.3136][DEBUG] 已刷新统计信息  
[2025-06-05 19:01:08.3136][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 19:01:39.8853][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:01:39.8911][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:01:39.8911][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:01:39.8911][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:01:39.8911][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:01:54.7294][DEBUG] 已更新分页，当前第1/1页，每页50条，共3条记录  
[2025-06-05 19:01:54.7294][DEBUG] 应用过滤器后，显示3/4条记录  
[2025-06-05 19:01:58.3513][DEBUG] 已更新分页，当前第1/1页，每页50条，共1条记录  
[2025-06-05 19:01:58.3513][DEBUG] 应用过滤器后，显示1/4条记录  
[2025-06-05 19:02:01.6497][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:02:01.6497][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:02:10.4646][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:02:10.4646][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:02:10.4646][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:02:10.4646][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:02:10.4646][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:02:40.4662][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:02:40.4662][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:02:40.4662][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:02:40.4662][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:02:40.4662][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:03:10.4659][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:03:10.4659][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:03:10.4659][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:03:10.4659][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:03:10.4659][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:03:40.4672][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:03:40.4723][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:03:40.4723][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:03:40.4723][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:03:40.4723][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:04:10.4766][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:04:10.4766][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 19:04:10.4766][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 19:04:10.4766][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 19:04:10.4766][DEBUG] 自动刷新历史记录完成  
[2025-06-05 19:04:11.6626][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 19:04:11.6626][DEBUG] 已刷新统计信息  
[2025-06-05 19:04:14.5478][DEBUG] 已停止自动刷新  
[2025-06-05 19:04:14.5478][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止  
[2025-06-05 19:04:16.8947][ INFO] 正在退出应用程序...  
[2025-06-05 19:04:16.8952][ INFO] 已停止所有运行中的脚本任务，模拟器保持运行状态  
[2025-06-05 19:04:16.8952][ INFO] 正在清理调度器资源...  
[2025-06-05 19:04:16.8952][ INFO] 调度器资源已清理完成，模拟器保持运行状态  
[2025-06-05 19:04:16.8952][ INFO] 应用程序即将关闭，模拟器将继续运行  
[2025-06-05 19:04:16.8952][ INFO] 托盘提示更新定时器已停止  
[2025-06-05 19:04:16.8952][ INFO] 托盘图标资源已释放  
[2025-06-05 19:04:16.8952][ INFO] 应用退出时已释放托盘图标资源  
[2025-06-05 19:04:16.9353][ INFO] AppConfig配置文件已成功备份到: C:\Users\<USER>\AppData\Roaming\DanDing1\AppConfig  
[2025-06-05 20:22:56.4304][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:22:56.4304][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:22:56.4304][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:22:56.4304][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:22:56.4304][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:22:56.4304][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 20:22:56.6551][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:22:56.8639][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:22:56.8639][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:22:56.9637][ INFO] 正在初始化后台调度器...  
[2025-06-05 20:22:56.9767][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 20:22:57.0930][ INFO] 已设置调度器视图模型实例  
[2025-06-05 20:22:57.0930][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 20:22:57.2636][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 20:22:57.2636][ INFO]   
[2025-06-05 20:22:57.2636][ INFO] =========DanDing-Started==========  
[2025-06-05 20:22:57.2636][ INFO]   
[2025-06-05 20:22:57.2636][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 20:22:59.1919][ INFO] 调度器已在后台启动完成  
[2025-06-05 20:22:59.6566][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:23:00.5440][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 20:23:01.1400][DEBUG] WebSocket连接成功  
[2025-06-05 20:23:01.1863][DEBUG] 常规消息，type=welcome  
[2025-06-05 20:23:01.1863][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1142","clientId":"xsllovemlj-client-1142","username":"xsllovemlj","timestamp":1749126110348}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 20:23:01.1863][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 20:23:01.1863][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:23:01.1863][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:23:01.1863][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:23:01.1863][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 20:23:01.9758][DEBUG] 常规消息，type=server-response  
[2025-06-05 20:23:03.5153][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 20:23:03.5153][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 20:23:56.4973][ INFO] 托盘提示更新定时器已停止  
[2025-06-05 20:23:56.4973][ INFO] 托盘图标资源已释放  
[2025-06-05 20:23:56.4973][ INFO] 应用退出时已释放托盘图标资源  
[2025-06-05 20:23:56.5533][ INFO] AppConfig配置文件已成功备份到: C:\Users\<USER>\AppData\Roaming\DanDing1\AppConfig  
[2025-06-05 20:44:20.3027][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:20.3027][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:20.3027][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:44:20.3027][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:44:20.3159][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:44:20.3159][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 20:44:20.6106][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:20.7368][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 20:44:21.0593][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:44:21.0593][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:44:21.2319][ INFO] 正在初始化后台调度器...  
[2025-06-05 20:44:21.2673][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 20:44:21.4003][ INFO] 已设置调度器视图模型实例  
[2025-06-05 20:44:21.4003][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 20:44:21.7284][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 20:44:21.7354][ INFO]   
[2025-06-05 20:44:21.7354][ INFO] =========DanDing-Started==========  
[2025-06-05 20:44:21.7354][ INFO]   
[2025-06-05 20:44:21.7354][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 20:44:23.5693][ INFO] 调度器已在后台启动完成  
[2025-06-05 20:44:23.6169][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:44:25.5037][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 20:44:25.6172][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 20:44:26.1229][DEBUG] WebSocket连接成功  
[2025-06-05 20:44:26.1322][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 20:44:26.1322][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:26.1322][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:26.1322][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:44:26.1322][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 20:44:27.0851][DEBUG] 常规消息，type=welcome  
[2025-06-05 20:44:27.0851][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1143","clientId":"xsllovemlj-client-1143","username":"xsllovemlj","timestamp":1749127395314}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 20:44:28.0535][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 20:44:28.0535][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 20:46:35.3528][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:35.3528][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:35.3602][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:46:35.3602][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:46:35.3602][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 20:46:35.3834][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 20:46:35.6748][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:35.7944][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 20:46:36.0695][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:46:36.0695][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:46:36.2242][ INFO] 正在初始化后台调度器...  
[2025-06-05 20:46:36.2524][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 20:46:36.3844][ INFO] 已设置调度器视图模型实例  
[2025-06-05 20:46:36.3844][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 20:46:36.7287][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 20:46:36.7287][ INFO]   
[2025-06-05 20:46:36.7287][ INFO] =========DanDing-Started==========  
[2025-06-05 20:46:36.7287][ INFO]   
[2025-06-05 20:46:36.7373][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 20:46:38.5241][ INFO] 调度器已在后台启动完成  
[2025-06-05 20:46:38.6799][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 20:46:40.5991][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 20:46:41.1772][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 20:46:41.5699][DEBUG] WebSocket连接成功  
[2025-06-05 20:46:41.6257][DEBUG] 常规消息，type=welcome  
[2025-06-05 20:46:41.6257][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1144","clientId":"xsllovemlj-client-1144","username":"xsllovemlj","timestamp":1749127530778}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 20:46:41.6257][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 20:46:41.6257][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:41.6257][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:41.6257][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 20:46:41.6257][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 20:46:43.0354][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 20:46:43.0354][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 20:48:38.0366][DEBUG] CheckUserStatus|Running...  
[2025-06-05 20:48:38.3840][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 21:05:14.8209][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:05:14.8209][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:05:14.8209][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:05:14.8209][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:05:14.8209][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:05:14.8391][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 21:05:15.2199][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:05:15.3425][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 21:05:15.6069][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:05:15.6069][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:05:15.7516][ INFO] 正在初始化后台调度器...  
[2025-06-05 21:05:15.7770][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:05:15.8909][ INFO] 已设置调度器视图模型实例  
[2025-06-05 21:05:15.8909][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 21:05:16.1846][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 21:05:16.1846][ INFO]   
[2025-06-05 21:05:16.1846][ INFO] =========DanDing-Started==========  
[2025-06-05 21:05:16.1846][ INFO]   
[2025-06-05 21:05:16.1846][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 21:05:18.0383][ INFO] 调度器已在后台启动完成  
[2025-06-05 21:05:18.2246][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:09:21.1529][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:21.1529][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:21.1529][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:09:21.1529][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:09:21.1708][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:09:21.1708][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 21:09:21.4900][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:21.6163][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 21:09:21.9363][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:09:21.9363][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:09:22.1468][ INFO] 正在初始化后台调度器...  
[2025-06-05 21:09:22.1822][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:09:22.3375][ INFO] 已设置调度器视图模型实例  
[2025-06-05 21:09:22.3375][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 21:09:22.7364][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 21:09:22.7364][ INFO]   
[2025-06-05 21:09:22.7364][ INFO] =========DanDing-Started==========  
[2025-06-05 21:09:22.7364][ INFO]   
[2025-06-05 21:09:22.7440][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 21:09:24.4743][ INFO] 调度器已在后台启动完成  
[2025-06-05 21:09:24.4955][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:09:26.4917][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 21:09:26.5375][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:09:26.9511][DEBUG] WebSocket连接成功  
[2025-06-05 21:09:26.9511][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 21:09:26.9511][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:26.9511][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:26.9511][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:09:26.9511][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 21:09:27.5258][DEBUG] 常规消息，type=welcome  
[2025-06-05 21:09:27.5258][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1145","clientId":"xsllovemlj-client-1145","username":"xsllovemlj","timestamp":1749128896155}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 21:09:28.9944][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 21:09:28.9944][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 21:09:55.1750][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:09:55.1750][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 21:09:55.1750][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 21:09:55.2044][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:09:55.2131][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:09:55.2131][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:09:55.2131][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:09:55.2131][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 21:09:55.2131][DEBUG] 已刷新统计信息  
[2025-06-05 21:09:55.2131][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 21:10:25.1670][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:10:25.1670][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:10:25.1670][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:10:25.1670][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:10:25.1670][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:10:55.1666][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:10:55.1666][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:10:55.1666][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:10:55.1666][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:10:55.1666][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:11:23.9957][DEBUG] CheckUserStatus|Running...  
[2025-06-05 21:11:24.3380][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 21:11:25.1662][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:11:25.1662][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:11:25.1662][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:11:25.1662][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:11:25.1662][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:12:04.3105][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:04.3105][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:04.3105][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:12:04.3105][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:12:04.3249][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:12:04.3249][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 21:12:04.6029][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:04.7215][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 21:12:05.0539][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:12:05.0539][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:12:05.2059][ INFO] 正在初始化后台调度器...  
[2025-06-05 21:12:05.2315][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:12:05.3509][ INFO] 已设置调度器视图模型实例  
[2025-06-05 21:12:05.3509][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 21:12:05.6954][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 21:12:05.6954][ INFO]   
[2025-06-05 21:12:05.6954][ INFO] =========DanDing-Started==========  
[2025-06-05 21:12:05.6954][ INFO]   
[2025-06-05 21:12:05.6954][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 21:12:07.4857][ INFO] 调度器已在后台启动完成  
[2025-06-05 21:12:07.6058][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:12:09.8847][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 21:12:10.3142][DEBUG] WebSocket连接成功  
[2025-06-05 21:12:10.3725][DEBUG] 常规消息，type=welcome  
[2025-06-05 21:12:10.3725][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1146","clientId":"xsllovemlj-client-1146","username":"xsllovemlj","timestamp":1749129059522}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 21:12:10.3725][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 21:12:10.3725][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:10.3725][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:10.3725][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:12:10.3725][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 21:12:11.9457][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 21:12:11.9457][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 21:12:13.9246][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:12:13.9246][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 21:12:13.9246][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 21:12:13.9533][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:12:13.9638][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:12:13.9638][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:12:13.9638][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:12:13.9638][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 21:12:13.9638][DEBUG] 已刷新统计信息  
[2025-06-05 21:12:13.9638][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 21:12:43.9198][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:12:43.9198][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:12:43.9198][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:12:43.9198][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:12:43.9198][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:13:13.9174][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:13:13.9174][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:13:13.9174][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:13:13.9174][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:13:13.9174][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:13:35.5330][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:35.5432][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:35.5432][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:13:35.5432][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:13:35.5432][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:13:35.5432][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 21:13:35.8187][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:35.9267][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 21:13:36.1835][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:13:36.1835][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:13:36.3204][ INFO] 正在初始化后台调度器...  
[2025-06-05 21:13:36.3465][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:13:36.4634][ INFO] 已设置调度器视图模型实例  
[2025-06-05 21:13:36.4634][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 21:13:36.8046][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 21:13:36.8101][ INFO]   
[2025-06-05 21:13:36.8101][ INFO] =========DanDing-Started==========  
[2025-06-05 21:13:36.8101][ INFO]   
[2025-06-05 21:13:36.8101][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 21:13:38.5855][ INFO] 调度器已在后台启动完成  
[2025-06-05 21:13:38.8227][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:13:40.9823][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 21:13:41.5282][DEBUG] WebSocket连接成功  
[2025-06-05 21:13:41.5821][DEBUG] 常规消息，type=welcome  
[2025-06-05 21:13:41.5821][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1149","clientId":"xsllovemlj-client-1149","username":"xsllovemlj","timestamp":1749129150609}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 21:13:41.5821][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 21:13:41.5821][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:41.5821][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:41.5821][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:13:41.5821][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 21:13:43.1125][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 21:13:43.1125][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 21:13:44.9385][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:13:44.9385][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 21:13:44.9385][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 21:13:44.9719][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:13:44.9797][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:13:44.9797][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:13:44.9797][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:13:44.9797][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 21:13:44.9797][DEBUG] 已刷新统计信息  
[2025-06-05 21:13:44.9797][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 21:14:14.9376][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:14:14.9376][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:14:14.9376][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:14:14.9376][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:14:14.9376][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:14:44.9369][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:14:44.9503][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:14:44.9503][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:14:44.9503][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:14:44.9503][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:15:14.9532][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:15:14.9532][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:15:14.9532][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:15:14.9532][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:15:14.9532][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:15:38.1144][DEBUG] CheckUserStatus|Running...  
[2025-06-05 21:15:38.4262][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 21:15:44.9524][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:15:44.9524][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:15:44.9524][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:15:44.9524][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:15:44.9524][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:16:14.9518][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:16:14.9518][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:16:14.9518][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:16:14.9518][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:16:14.9518][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:16:44.9523][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:16:44.9523][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:16:44.9523][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:16:44.9523][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:16:44.9523][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:17:14.9519][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:17:14.9519][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:17:14.9519][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:17:14.9519][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:17:14.9519][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:17:44.9535][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:17:44.9535][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:17:44.9535][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:17:44.9535][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:17:44.9535][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:18:00.6458][DEBUG] 已停止自动刷新  
[2025-06-05 21:18:00.6458][DEBUG] 任务历史记录窗口已关闭，自动刷新已停止  
[2025-06-05 21:18:02.1600][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:18:02.1600][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 21:18:02.1600][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 21:18:02.1745][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:18:02.1745][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:18:02.1745][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:18:02.1745][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:18:02.1745][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 21:18:02.1745][DEBUG] 已刷新统计信息  
[2025-06-05 21:18:02.1745][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 21:18:32.1549][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:18:32.1549][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:18:32.1549][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:18:32.1549][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:18:32.1549][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:29:36.4405][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:36.4545][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:36.4545][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:29:36.4545][DEBUG] ScriptSyncService: 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:29:36.4545][DEBUG] ScriptSyncService: 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器  
[2025-06-05 21:29:36.4545][DEBUG] ScriptSyncService已初始化，WebSocket消息处理器已注册  
[2025-06-05 21:29:36.7322][DEBUG] ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:36.8426][DEBUG] 图库版本参数被默认为: 888  
[2025-06-05 21:29:37.1092][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:29:37.1092][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:29:37.2616][ INFO] 正在初始化后台调度器...  
[2025-06-05 21:29:37.2910][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:29:37.4044][ INFO] 已设置调度器视图模型实例  
[2025-06-05 21:29:37.4044][ INFO] 根据用户设置，在后台自动启动调度器  
[2025-06-05 21:29:37.7244][DEBUG] 初始化.. 删除三天前的日志文件夹..  
[2025-06-05 21:29:37.7244][ INFO]   
[2025-06-05 21:29:37.7244][ INFO] =========DanDing-Started==========  
[2025-06-05 21:29:37.7244][ INFO]   
[2025-06-05 21:29:37.7244][ INFO] 日志模块：初始化完成.. 用户自定义名称载入成功..  
[2025-06-05 21:29:39.5412][ INFO] 调度器已在后台启动完成  
[2025-06-05 21:29:39.7377][DEBUG] WebSocket连接未打开，无法发送对象  
[2025-06-05 21:29:41.1108][DEBUG] 正在连接到WebSocket服务器  
[2025-06-05 21:29:41.5386][DEBUG] WebSocket连接成功  
[2025-06-05 21:29:41.5945][DEBUG] 常规消息，type=welcome  
[2025-06-05 21:29:41.5945][DEBUG] 收到WebSocket消息但没有注册处理器: {"type":"welcome","message":"欢迎连接到WebSocket服务器，您的客户端ID是: xsllovemlj-client-1151","clientId":"xsllovemlj-client-1151","username":"xsllovemlj","timestamp":1749130110751}，已注册的处理器类型: TRIGGER_SCRIPT_STATUS_SYNC_COMMAND, GET_LOGS_COMMAND  
[2025-06-05 21:29:41.5995][DEBUG] 成功连接到WebSocket服务器  
[2025-06-05 21:29:41.5995][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:41.5995][DEBUG] LogSyncService: 已初始化，已注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:41.5995][DEBUG] LogSyncService: 已重新注册GET_LOGS_COMMAND消息处理器  
[2025-06-05 21:29:41.5995][DEBUG] WebSocket连接成功后，已重新注册消息处理器  
[2025-06-05 21:29:43.9712][DEBUG] InitMuMuConfigs|Running...  
[2025-06-05 21:29:43.9712][DEBUG] InitMuMuConfigs|已有MuMu路径配置且路径有效  
[2025-06-05 21:31:38.9725][DEBUG] CheckUserStatus|Running...  
[2025-06-05 21:31:39.2808][DEBUG] CheckUserStatus|Ended|OK  
[2025-06-05 21:33:41.6294][ INFO] 任务历史记录服务初始化完成，存储路径：C:\Users\<USER>\AppData\Roaming\DanDing1\SchedulerHistory  
[2025-06-05 21:33:41.6294][DEBUG] 已启动自动刷新，间隔：30秒  
[2025-06-05 21:33:41.6294][DEBUG] 任务历史记录视图模型已初始化  
[2025-06-05 21:33:41.6661][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:33:41.6741][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:33:41.6741][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:33:41.6741][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:33:41.6741][DEBUG] 加载2025-05-30至2025-06-05的历史记录，共4条  
[2025-06-05 21:33:41.6741][DEBUG] 已刷新统计信息  
[2025-06-05 21:33:41.6741][DEBUG] 任务历史记录视图模型已初始化完成  
[2025-06-05 21:34:11.6285][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:34:11.6285][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:34:11.6285][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:34:11.6285][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:34:11.6285][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:34:23.6841][DEBUG] 已更新分页，当前第1/1页，每页20条，共4条记录  
[2025-06-05 21:34:24.5204][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:34:41.6280][DEBUG] 加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:34:41.6280][DEBUG] 已更新分页，当前第1/1页，每页50条，共4条记录  
[2025-06-05 21:34:41.6280][DEBUG] 应用过滤器后，显示4/4条记录  
[2025-06-05 21:34:41.6280][ INFO] 已加载2025-05-29至2025-06-05的历史记录，共4条  
[2025-06-05 21:34:41.6280][DEBUG] 自动刷新历史记录完成  
[2025-06-05 21:34:46.5015][ INFO] 托盘提示更新定时器已停止  
[2025-06-05 21:34:46.5056][ INFO] 托盘图标资源已释放  
[2025-06-05 21:34:46.5056][ INFO] 应用退出时已释放托盘图标资源  
[2025-06-05 21:34:46.5406][ INFO] AppConfig配置文件已成功备份到: C:\Users\<USER>\AppData\Roaming\DanDing1\AppConfig  
