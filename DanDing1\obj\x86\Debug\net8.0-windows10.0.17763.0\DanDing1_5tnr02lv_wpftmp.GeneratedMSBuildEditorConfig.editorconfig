is_global = true
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = true
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotWarningLevel = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net8.0-windows10.0.17763.0
build_property.TargetPlatformMinVersion = 10.0.17763.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DanDing1
build_property.ProjectDir = C:\Users\<USER>\source\repos\DanDing1SubProject\DanDing1\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTAotExportsEnabled = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptIn = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptOut = 
build_property.CsWinRTCcwLookupTableGeneratorEnabled = true
build_property.CsWinRTMergeReferencedActivationFactories = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 
