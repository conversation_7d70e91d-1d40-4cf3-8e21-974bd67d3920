﻿#pragma checksum "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5DD9EEF2C0DA04869A7BE523BB99ECC39781D019"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Models.Super;
using DanDing1.ViewModels.Windows;
using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// SuperMultiGameConfigWindow
    /// </summary>
    public partial class SuperMultiGameConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.Windows.SuperMultiGameConfigWindow SuperMultiGamesWindowInstance;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OverNotice;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/supermultigameconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SuperMultiGamesWindowInstance = ((DanDing1.Views.Windows.SuperMultiGameConfigWindow)(target));
            return;
            case 2:
            
            #line 64 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Checked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            
            #line 65 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Unchecked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OverNotice = ((System.Windows.Controls.CheckBox)(target));
            
            #line 74 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
            this.OverNotice.Checked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            
            #line 75 "..\..\..\..\..\Views\Windows\SuperMultiGameConfigWindow.xaml"
            this.OverNotice.Unchecked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

