﻿#pragma checksum "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "95353903318754663598821424E83BFD2D80450D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Models.Super;
using DanDing1.ViewModels.Windows;
using DanDing1.Views.UserControls;
using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// SuperMultiGamesWindow
    /// </summary>
    public partial class SuperMultiGamesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 13 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.Windows.SuperMultiGamesWindow SuperMultiGamesWindowInstance;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.UserControls.AddTaskControl AddTask;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.DataGrid DataGrid;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl LogTabControl;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.UserControls.MultiGamePreviewControl GamePreview;
        
        #line default
        #line hidden
        
        
        #line 452 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.UserControls.NotificationsPanel NotificationsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/supermultigameswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SuperMultiGamesWindowInstance = ((DanDing1.Views.Windows.SuperMultiGamesWindow)(target));
            
            #line 24 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
            this.SuperMultiGamesWindowInstance.Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            
            #line 25 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
            this.SuperMultiGamesWindowInstance.Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.AddTask = ((DanDing1.Views.UserControls.AddTaskControl)(target));
            return;
            case 4:
            this.DataGrid = ((Wpf.Ui.Controls.DataGrid)(target));
            
            #line 117 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
            this.DataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 121 "..\..\..\..\..\Views\Windows\SuperMultiGamesWindow.xaml"
            this.DataGrid.PreviewMouseRightButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.DataGrid_PreviewMouseRightButtonDown);
            
            #line default
            #line hidden
            return;
            case 5:
            this.LogTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 6:
            this.GamePreview = ((DanDing1.Views.UserControls.MultiGamePreviewControl)(target));
            return;
            case 7:
            this.NotificationsPanel = ((DanDing1.Views.UserControls.NotificationsPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

