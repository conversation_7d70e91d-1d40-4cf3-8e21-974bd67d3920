﻿#pragma checksum "..\..\..\..\..\Views\Windows\TeamWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9F62882928DEC1DFBA2790C117E8856A8E5E7CF6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// TeamWindow
    /// </summary>
    public partial class TeamWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBlock Screenshot_HWND;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Screenshot_ID;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Image Screenshot;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBlock Ocr_Name;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TeamName;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBlock Status_Tip;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ListBox_TeamName;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/teamwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Screenshot_HWND = ((Wpf.Ui.Controls.TextBlock)(target));
            return;
            case 2:
            this.Screenshot_ID = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            
            #line 44 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.Screenshot = ((Wpf.Ui.Controls.Image)(target));
            return;
            case 5:
            this.Ocr_Name = ((Wpf.Ui.Controls.TextBlock)(target));
            
            #line 61 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            this.Ocr_Name.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Ocr_Name_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TeamName = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            
            #line 69 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_1);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Status_Tip = ((Wpf.Ui.Controls.TextBlock)(target));
            return;
            case 9:
            this.ListBox_TeamName = ((System.Windows.Controls.ListBox)(target));
            return;
            case 10:
            
            #line 136 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_2);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 140 "..\..\..\..\..\Views\Windows\TeamWindow.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_3);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

